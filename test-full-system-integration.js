// Comprehensive test for full system integration
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

// Helper function to make authenticated requests
async function callEdgeFunction(functionName, body = {}, method = 'POST') {
  const headers = {
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'apikey': SUPABASE_ANON_KEY,
    'Content-Type': 'application/json',
  };

  const config = { method, headers };
  if (method !== 'GET' && body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, config);
  
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    data: response.ok ? await response.json() : await response.text()
  };
}

// Test RAG functionality with context
async function testRAGWithContext() {
  console.log('🔍 Testing RAG with Context...');
  
  const testCases = [
    {
      message: "Find me innovators working on fintech solutions in Zimbabwe",
      description: "RAG query for specific content",
      expectedRoute: "rag",
      expectContext: true
    },
    {
      message: "Tell me about mentors with AI expertise on the platform",
      description: "RAG query for mentor profiles",
      expectedRoute: "rag", 
      expectContext: true
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n📋 Testing: ${testCase.description}`);
      console.log(`   Query: "${testCase.message}"`);
      
      const result = await callEdgeFunction('ai-chat', {
        message: testCase.message,
        user_context: { user_id: null },
        rag_enabled: true,
        max_context_items: 10
      });

      if (result.ok) {
        const data = result.data;
        console.log(`✅ Success!`);
        console.log(`   Route: ${data.query_route} (expected: ${testCase.expectedRoute})`);
        console.log(`   Context items: ${data.rag_context_used?.length || 0}`);
        console.log(`   Processing time: ${data.processing_time_ms}ms`);
        
        if (testCase.expectContext && data.rag_context_used?.length > 0) {
          console.log(`   ✅ Context retrieved successfully`);
          data.rag_context_used.forEach((ctx, i) => {
            console.log(`     ${i+1}. [${ctx.type}] ${ctx.snippet} (${(ctx.relevance * 100).toFixed(0)}% relevance)`);
          });
        } else if (testCase.expectContext) {
          console.log(`   ⚠️ Expected context but none retrieved`);
        }
        
        console.log(`   Response preview: "${data.message.substring(0, 100)}..."`);
      } else {
        console.log(`❌ Failed: ${result.status} - ${result.data}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

// Test Text2SQL functionality
async function testText2SQLWithAnalytics() {
  console.log('\n📊 Testing Text2SQL with Analytics...');
  
  const testCases = [
    {
      message: "How many innovators are registered on the platform?",
      description: "Count query",
      expectedRoute: "text2sql"
    },
    {
      message: "What are the statistics for mentor profiles?",
      description: "Analytics query",
      expectedRoute: "text2sql"
    },
    {
      message: "Show me platform growth metrics",
      description: "Metrics query", 
      expectedRoute: "text2sql"
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n📈 Testing: ${testCase.description}`);
      console.log(`   Query: "${testCase.message}"`);
      
      const result = await callEdgeFunction('ai-chat', {
        message: testCase.message,
        user_context: { user_id: null },
        rag_enabled: true,
        max_context_items: 5
      });

      if (result.ok) {
        const data = result.data;
        console.log(`✅ Success!`);
        console.log(`   Route: ${data.query_route} (expected: ${testCase.expectedRoute})`);
        console.log(`   Context items: ${data.rag_context_used?.length || 0}`);
        
        // Check for analytics context
        const analyticsContext = data.rag_context_used?.filter(ctx => ctx.source === 'analytics_data');
        if (analyticsContext?.length > 0) {
          console.log(`   ✅ Analytics context included`);
        }
        
        console.log(`   Processing time: ${data.processing_time_ms}ms`);
        console.log(`   Response preview: "${data.message.substring(0, 100)}..."`);
      } else {
        console.log(`❌ Failed: ${result.status} - ${result.data}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

// Test authentication awareness
async function testAuthenticationAwareness() {
  console.log('\n🔐 Testing Authentication Awareness...');
  
  const testCases = [
    {
      userContext: { user_id: null },
      description: "Unauthenticated user",
      expectAuthAware: true
    },
    {
      userContext: { 
        user_id: "test-user-123",
        profile_type: "innovator",
        profile_status: "completed"
      },
      description: "Authenticated innovator",
      expectAuthAware: true
    },
    {
      userContext: {
        user_id: "test-user-456", 
        profile_type: "mentor",
        profile_status: "in_progress"
      },
      description: "Authenticated mentor with incomplete profile",
      expectAuthAware: true
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n👤 Testing: ${testCase.description}`);
      
      const result = await callEdgeFunction('ai-chat', {
        message: "What can I do on this platform?",
        user_context: testCase.userContext,
        rag_enabled: true,
        max_context_items: 3
      });

      if (result.ok) {
        const data = result.data;
        console.log(`✅ Success!`);
        console.log(`   Route: ${data.query_route}`);
        console.log(`   Processing time: ${data.processing_time_ms}ms`);
        
        // Check if response is authentication-aware
        const response = data.message.toLowerCase();
        if (testCase.userContext.user_id) {
          if (response.includes('profile') || response.includes('dashboard') || response.includes(testCase.userContext.profile_type)) {
            console.log(`   ✅ Response is authentication-aware`);
          } else {
            console.log(`   ⚠️ Response may not be fully authentication-aware`);
          }
        } else {
          if (response.includes('sign up') || response.includes('register') || response.includes('create')) {
            console.log(`   ✅ Response is appropriate for unauthenticated user`);
          } else {
            console.log(`   ⚠️ Response may not be appropriate for unauthenticated user`);
          }
        }
        
        console.log(`   Response preview: "${data.message.substring(0, 150)}..."`);
      } else {
        console.log(`❌ Failed: ${result.status} - ${result.data}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

// Test RAG population and embeddings
async function testRAGPopulationStatus() {
  console.log('\n📚 Testing RAG Population Status...');
  
  try {
    // Check current RAG system status
    const result = await callEdgeFunction('populate-rag-embeddings', {}, 'GET');
    
    if (result.ok) {
      const data = result.data;
      console.log(`✅ RAG System Status:`);
      console.log(`   Success: ${data.success}`);
      
      if (data.rag_system_status) {
        data.rag_system_status.forEach(metric => {
          console.log(`   ${metric.metric_name}: ${metric.metric_value} (${metric.status})`);
        });
      }
    } else {
      console.log(`❌ Failed to get RAG status: ${result.status} - ${result.data}`);
    }
    
    // Test dry run population
    console.log('\n📊 Testing RAG Population (Dry Run)...');
    const populationResult = await callEdgeFunction('populate-rag-embeddings', {
      content_types: ['profile'],
      batch_size: 5,
      dry_run: true
    });
    
    if (populationResult.ok) {
      const data = populationResult.data;
      console.log(`✅ RAG Population Test:`);
      console.log(`   Content types: ${data.content_processed.length}`);
      data.content_processed.forEach(content => {
        console.log(`   ${content.content_type}: ${content.records_found} records available`);
      });
      console.log(`   Processing time: ${data.processing_time_ms}ms`);
    } else {
      console.log(`❌ RAG Population failed: ${populationResult.status} - ${populationResult.data}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

// Run comprehensive integration tests
async function runFullSystemIntegrationTests() {
  console.log('🚀 Starting Full System Integration Tests...\n');
  console.log('=' .repeat(80));
  
  await testRAGWithContext();
  console.log('\n' + '=' .repeat(80));
  
  await testText2SQLWithAnalytics();
  console.log('\n' + '=' .repeat(80));
  
  await testAuthenticationAwareness();
  console.log('\n' + '=' .repeat(80));
  
  await testRAGPopulationStatus();
  console.log('\n' + '=' .repeat(80));
  
  console.log('\n🎯 Integration Test Summary:');
  console.log('✅ RAG with Context: Tested content retrieval and relevance');
  console.log('✅ Text2SQL with Analytics: Tested data queries and metrics');
  console.log('✅ Authentication Awareness: Tested user context handling');
  console.log('✅ RAG Population: Tested embedding system status');
  
  console.log('\n📋 Next Steps for Frontend Testing:');
  console.log('1. Test AI trigger buttons in browser');
  console.log('2. Verify authentication status after refresh');
  console.log('3. Check action button functionality');
  console.log('4. Validate caching behavior');
  console.log('5. Test user context propagation');
}

runFullSystemIntegrationTests();
