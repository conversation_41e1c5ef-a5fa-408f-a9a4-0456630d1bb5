<template>
  <q-card class="profile-ai-triggers-card">
    <q-card-section>
      <div class="row items-center q-mb-md">
        <q-icon name="psychology" color="primary" size="md" class="q-mr-sm" />
        <div class="text-h6 text-primary">AI Assistance for {{ formatProfileType }}</div>
      </div>

      <div class="text-body2 text-grey-7 q-mb-md">
        Get personalized AI help based on your profile and current needs
      </div>

      <!-- Profile Enhancement Triggers -->
      <div v-if="showProfileEnhancement" class="trigger-section q-mb-md">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="person" color="orange" size="sm" class="q-mr-xs" />
          Profile Enhancement
        </div>
        <div class="row q-gutter-xs">
          <q-btn
            v-for="trigger in profileEnhancementTriggers"
            :key="trigger.key"
            @click="handleTrigger(trigger.key)"
            :color="trigger.color"
            :icon="trigger.icon"
            :label="trigger.label"
            size="sm"
            outline
            class="trigger-btn"
          >
            <q-tooltip>{{ trigger.tooltip }}</q-tooltip>
          </q-btn>
        </div>
      </div>

      <!-- Matchmaking Triggers -->
      <div v-if="showMatchmaking" class="trigger-section q-mb-md">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="people" color="green" size="sm" class="q-mr-xs" />
          Smart Matchmaking
        </div>
        <div class="row q-gutter-xs">
          <q-btn
            v-for="trigger in matchmakingTriggers"
            :key="trigger.key"
            @click="handleTrigger(trigger.key)"
            :color="trigger.color"
            :icon="trigger.icon"
            :label="trigger.label"
            size="sm"
            outline
            class="trigger-btn"
          >
            <q-tooltip>{{ trigger.tooltip }}</q-tooltip>
          </q-btn>
        </div>
      </div>

      <!-- Quick Actions -->
      <div v-if="showQuickActions" class="trigger-section">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="flash_on" color="blue" size="sm" class="q-mr-xs" />
          Quick Actions
        </div>
        <div class="row q-gutter-xs">
          <q-btn
            v-for="trigger in quickActionTriggers"
            :key="trigger.key"
            @click="handleTrigger(trigger.key)"
            :color="trigger.color"
            :icon="trigger.icon"
            :label="trigger.label"
            size="sm"
            outline
            class="trigger-btn"
          >
            <q-tooltip>{{ trigger.tooltip }}</q-tooltip>
          </q-btn>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { formatProfileType as formatType } from '../../services/profileTypes'

interface Props {
  context?: string
  showMatchmaking?: boolean
  showProfileEnhancement?: boolean
  showQuickActions?: boolean
  profileType?: string
}

const props = withDefaults(defineProps<Props>(), {
  context: 'dashboard',
  showMatchmaking: true,
  showProfileEnhancement: true,
  showQuickActions: true
})

const globalServices = useGlobalServicesStore()

const formatProfileType = computed(() => {
  return props.profileType ? formatType(props.profileType) : 'User'
})

const profileEnhancementTriggers = [
  {
    key: 'complete_profile',
    label: 'Complete Profile',
    icon: 'person_add',
    color: 'orange',
    tooltip: 'Get help completing your profile'
  },
  {
    key: 'optimize_profile',
    label: 'Optimize Profile',
    icon: 'tune',
    color: 'purple',
    tooltip: 'Get suggestions to improve your profile'
  }
]

const matchmakingTriggers = [
  {
    key: 'find_connections',
    label: 'Find Connections',
    icon: 'people',
    color: 'green',
    tooltip: 'Find people to connect with'
  },
  {
    key: 'collaboration_opportunities',
    label: 'Find Collaborations',
    icon: 'handshake',
    color: 'teal',
    tooltip: 'Discover collaboration opportunities'
  }
]

const quickActionTriggers = [
  {
    key: 'discover_content',
    label: 'Discover Content',
    icon: 'explore',
    color: 'blue',
    tooltip: 'Find relevant content and discussions'
  },
  {
    key: 'get_started',
    label: 'Get Started',
    icon: 'rocket_launch',
    color: 'primary',
    tooltip: 'Get help getting started on the platform'
  }
]

const handleTrigger = async (triggerKey: string) => {
  try {
    await globalServices.aiChatTriggerService.triggerChat(
      triggerKey, 
      `${props.context}-${props.profileType || 'user'}`
    )
  } catch (error) {
    console.error('Error triggering AI chat:', error)
  }
}
</script>

<style scoped>
.profile-ai-triggers-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.trigger-section {
  padding: 8px 0;
}

.trigger-btn {
  transition: all 0.2s ease;
}

.trigger-btn:hover {
  transform: translateY(-1px);
}
</style>
