// Test script to manually trigger logout functionality
// Run this in the browser console to test logout

console.log('Testing logout functionality...');

// Method 1: Try to access through global services
if (window.__GLOBAL_SERVICES__) {
  console.log('Global services found:', Object.keys(window.__GLOBAL_SERVICES__));
  
  if (window.__GLOBAL_SERVICES__.authStore) {
    console.log('Auth store found in global services');
    if (window.__GLOBAL_SERVICES__.authStore.signOut) {
      console.log('Triggering logout via global services...');
      window.__GLOBAL_SERVICES__.authStore.signOut();
    } else {
      console.log('signOut method not found in auth store');
    }
  } else {
    console.log('Auth store not found in global services');
  }
} else {
  console.log('Global services not found');
}

// Method 2: Try to access through Vue app instance
if (window.Vue) {
  console.log('Vue found, trying to access app instance...');
  
  // Try to find the Vue app instance
  const appElements = document.querySelectorAll('[data-v-app]');
  if (appElements.length > 0) {
    console.log('Found Vue app elements:', appElements.length);
  }
}

// Method 3: Try to trigger logout by simulating button click
const accountButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
  btn.textContent && btn.textContent.includes('Account')
);

if (accountButtons.length > 0) {
  console.log('Found Account button, attempting to click...');
  accountButtons[0].click();
  
  // Wait a bit and then look for logout option
  setTimeout(() => {
    const logoutItems = Array.from(document.querySelectorAll('[role="menuitem"], .q-item')).filter(item => 
      item.textContent && item.textContent.includes('Logout')
    );
    
    if (logoutItems.length > 0) {
      console.log('Found Logout option, clicking...');
      logoutItems[0].click();
    } else {
      console.log('Logout option not found after clicking Account button');
    }
  }, 500);
} else {
  console.log('Account button not found');
}

// Method 4: Check if Supabase client is available
if (window.supabase) {
  console.log('Supabase client found, attempting direct logout...');
  window.supabase.auth.signOut().then(() => {
    console.log('Direct Supabase logout successful');
    window.location.href = '/';
  }).catch(error => {
    console.error('Direct Supabase logout failed:', error);
  });
} else {
  console.log('Supabase client not found in window');
}

console.log('Logout test script completed');
