/**
 * AI Chat Service - Clean Implementation
 * 
 * Handles all AI chat functionality with proper error handling
 * and response processing
 */

import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/stores/auth'

// Types
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp?: Date
  id?: string
}

export interface UserContext {
  is_authenticated: boolean
  user_id?: string
  profile_type?: string
  current_page?: string
  profile_data?: any
}

export interface AIChatRequest {
  message: string
  conversation_history?: ChatMessage[]
  user_context: UserContext
  rag_enabled?: boolean
  max_context_items?: number
}

export interface AIActionButton {
  id: string
  label: string
  icon?: string
  color?: string
  action_type: 'navigation' | 'dialog' | 'trigger' | 'external'
  action_data: {
    route?: string
    dialog?: string
    trigger_key?: string
    message?: string
    url?: string
    params?: Record<string, any>
  }
  tooltip?: string
  requires_auth?: boolean
}

export interface AIChatResponse {
  success: boolean
  message?: string
  action_buttons?: AIActionButton[]
  rag_context_used?: any[]
  processing_time_ms?: number
  query_route?: string
  error?: string
}

class AIChatService {
  private baseUrl: string

  constructor() {
    this.baseUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`
  }

  /**
   * Get user context for AI requests
   */
  private getUserContext(currentPage?: string): UserContext {
    const authStore = useAuthStore()
    const user = authStore.user
    const profile = authStore.profile

    return {
      is_authenticated: !!user,
      user_id: user?.id,
      profile_type: profile?.profile_type,
      current_page: currentPage || this.getCurrentPage(),
      profile_data: profile
    }
  }

  /**
   * Get current page from URL
   */
  private getCurrentPage(): string {
    const path = window.location.pathname
    if (path === '/') return 'home'
    if (path.includes('/dashboard')) return 'dashboard'
    if (path.includes('/virtual-community')) return 'virtual-community'
    if (path.includes('/about')) return 'about'
    return 'unknown'
  }

  /**
   * Send message to AI and get response
   */
  async sendMessage(
    message: string,
    conversationHistory: ChatMessage[] = [],
    currentPage?: string
  ): Promise<AIChatResponse> {
    try {
      console.log('🤖 Sending AI message:', { message: message.substring(0, 50) + '...', currentPage })

      const userContext = this.getUserContext(currentPage)
      
      const requestBody: AIChatRequest = {
        message: message.trim(),
        conversation_history: conversationHistory.slice(-6), // Keep last 6 messages
        user_context: userContext,
        rag_enabled: true,
        max_context_items: 8
      }

      console.log('📤 Request payload:', {
        messageLength: requestBody.message.length,
        historyLength: requestBody.conversation_history?.length || 0,
        userAuthenticated: userContext.is_authenticated,
        currentPage: userContext.current_page
      })

      console.log('🔧 Environment check:', {
        supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
        hasAnonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
        anonKeyLength: import.meta.env.VITE_SUPABASE_ANON_KEY?.length || 0,
        fullUrl: `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/ai-chat`
      })

      // Call the AI chat Edge Function using direct fetch (like working examples)
      const httpResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/ai-chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      console.log('📥 Raw response status:', httpResponse.status, httpResponse.statusText)

      if (!httpResponse.ok) {
        const errorText = await httpResponse.text()
        console.error('❌ Edge Function error:', httpResponse.status, errorText)
        throw new Error(`AI service error: ${httpResponse.status} - ${errorText}`)
      }

      const data = await httpResponse.json()
      console.log('📥 Response data:', {
        success: data.success,
        hasMessage: !!data.message,
        messageLength: data.message?.length || 0
      })

      // Handle different response formats
      let response: AIChatResponse
      
      if (data.success === false) {
        console.error('❌ AI service returned error:', data.error)
        throw new Error(data.error || 'AI service returned an error')
      }

      if (data.message) {
        // New format: { success: true, message: "...", ... }
        response = data as AIChatResponse
        console.log('✅ AI response received (new format):', {
          messageLength: response.message?.length || 0,
          processingTime: response.processing_time_ms,
          queryRoute: response.query_route
        })
      } else if (data.response) {
        // Legacy format: { response: "...", ... }
        response = {
          success: true,
          message: data.response,
          processing_time_ms: data.processing_time_ms,
          query_route: data.query_route
        }
        console.log('✅ AI response received (legacy format):', {
          messageLength: response.message?.length || 0,
          processingTime: response.processing_time_ms
        })
      } else {
        console.error('❌ Invalid response format:', data)
        throw new Error('Invalid response format from AI service')
      }

      // Validate response content
      if (!response.message || response.message.trim().length === 0) {
        console.error('❌ Empty AI response received')
        throw new Error('AI service returned an empty response')
      }

      // Enhance response with profile-aware recommendations if applicable
      response = await this.enhanceWithProfileRecommendations(
        response,
        userContext,
        message,
        conversationHistory
      )

      // Generate context-aware action buttons if not provided by AI
      if (!response.action_buttons || response.action_buttons.length === 0) {
        response.action_buttons = await this.generateContextualActionButtons(
          response.message,
          userContext,
          conversationHistory
        )
      }

      return response

    } catch (error: any) {
      console.error('💥 AI Chat Service Error:', {
        message: error.message,
        stack: error.stack,
        type: typeof error
      })

      // Return structured error response
      return {
        success: false,
        error: error.message || 'Unknown error occurred',
        processing_time_ms: 0
      }
    }
  }

  /**
   * Enhance AI response with profile-aware recommendations
   */
  private async enhanceWithProfileRecommendations(
    response: AIChatResponse,
    userContext: UserContext,
    userMessage: string,
    conversationHistory: ChatMessage[]
  ): Promise<AIChatResponse> {
    try {
      // Check if this is a matchmaking-related query
      const isMatchmakingQuery = this.isMatchmakingQuery(userMessage, conversationHistory)

      if (!isMatchmakingQuery || !userContext.is_authenticated) {
        return response
      }

      console.log('🎯 Enhancing response with profile recommendations')

      // Import profile matching service
      const { useProfileMatchingService } = await import('./profileMatchingService')
      const profileMatchingService = useProfileMatchingService()

      // Determine matching criteria based on user message and profile
      const criteria = this.extractMatchingCriteria(userMessage, userContext)

      // Find profile matches
      const matches = await profileMatchingService.findMatches(userContext.profile_data, criteria)

      if (matches.length > 0) {
        // Enhance the AI response with specific recommendations
        const enhancedMessage = this.formatProfileRecommendations(response.message || '', matches, userContext)

        // Add action buttons for connecting with recommended profiles
        const recommendationActions = this.generateRecommendationActions(matches)

        return {
          ...response,
          message: enhancedMessage,
          action_buttons: [...(response.action_buttons || []), ...recommendationActions]
        }
      }

      return response

    } catch (error) {
      console.error('❌ Error enhancing with profile recommendations:', error)
      return response // Return original response on error
    }
  }

  /**
   * Check if the user message is related to matchmaking/networking
   */
  private isMatchmakingQuery(userMessage: string, conversationHistory: ChatMessage[]): boolean {
    const matchmakingKeywords = [
      'connect', 'find', 'discover', 'match', 'recommend', 'network', 'meet',
      'innovators', 'investors', 'mentors', 'entrepreneurs', 'partners',
      'collaboration', 'opportunities', 'projects', 'investment'
    ]

    const messageLower = userMessage.toLowerCase()
    return matchmakingKeywords.some(keyword => messageLower.includes(keyword))
  }

  /**
   * Extract matching criteria from user message and context
   */
  private extractMatchingCriteria(userMessage: string, userContext: UserContext): any {
    const criteria: any = {
      exclude_user_id: userContext.user_id,
      limit: 5
    }

    const messageLower = userMessage.toLowerCase()

    // Determine target profile types based on user's profile and message
    if (userContext.profile_type === 'investor') {
      if (messageLower.includes('innovator') || messageLower.includes('entrepreneur')) {
        criteria.profile_type = ['innovator', 'entrepreneur', 'startup']
      }
    } else if (userContext.profile_type === 'innovator') {
      if (messageLower.includes('investor') || messageLower.includes('funding')) {
        criteria.profile_type = ['investor']
      } else if (messageLower.includes('mentor')) {
        criteria.profile_type = ['mentor', 'expert']
      }
    } else if (userContext.profile_type === 'mentor') {
      criteria.profile_type = ['innovator', 'entrepreneur', 'student']
    }

    // If no specific type mentioned, use recommended types
    if (!criteria.profile_type) {
      const { useProfileMatchingService } = require('./profileMatchingService')
      const profileMatchingService = useProfileMatchingService()
      criteria.profile_type = profileMatchingService.getRecommendedProfileTypes(userContext.profile_type || 'user')
    }

    return criteria
  }

  /**
   * Format profile recommendations into enhanced AI response
   */
  private formatProfileRecommendations(originalMessage: string, matches: any[], userContext: UserContext): string {
    const topMatches = matches.slice(0, 3) // Show top 3 matches

    let enhancedMessage = originalMessage + '\n\n**🎯 Personalized Recommendations:**\n\n'

    topMatches.forEach((match, index) => {
      const name = match.profile_name || `${match.first_name || ''} ${match.last_name || ''}`.trim() || 'User'
      const score = match.compatibility_score
      const reasons = match.match_reasons.slice(0, 2).join(', ')

      enhancedMessage += `**${index + 1}. ${name}** (${match.profile_type})\n`
      enhancedMessage += `• Compatibility: ${score}% match\n`
      enhancedMessage += `• Why: ${reasons}\n`
      if (match.bio) {
        enhancedMessage += `• About: ${match.bio.substring(0, 100)}${match.bio.length > 100 ? '...' : ''}\n`
      }
      enhancedMessage += '\n'
    })

    enhancedMessage += `Found ${matches.length} total matches. These recommendations are based on your profile, interests, and compatibility analysis.`

    return enhancedMessage
  }

  /**
   * Generate action buttons for profile recommendations
   */
  private generateRecommendationActions(matches: any[]): AIActionButton[] {
    const actions: AIActionButton[] = []

    // Add "View All Matches" action
    actions.push({
      id: 'view-all-matches',
      label: 'View All Matches',
      icon: 'people',
      color: 'primary',
      action_type: 'navigation',
      action_data: { route: '/virtual-community', params: { tab: 'profiles' } },
      tooltip: 'Browse all matching profiles'
    })

    // Add action for top match if available
    if (matches.length > 0) {
      const topMatch = matches[0]
      actions.push({
        id: 'connect-top-match',
        label: `Connect with ${topMatch.profile_name || 'Top Match'}`,
        icon: 'person_add',
        color: 'secondary',
        action_type: 'dialog',
        action_data: {
          dialog: 'message-user',
          params: {
            userId: topMatch.user_id,
            userName: topMatch.profile_name
          }
        },
        tooltip: 'Send a message to this recommended connection',
        requires_auth: true
      })
    }

    return actions
  }

  /**
   * Generate contextual action buttons based on AI response and user context
   */
  private async generateContextualActionButtons(
    aiMessage: string,
    userContext: UserContext,
    conversationHistory: ChatMessage[]
  ): Promise<AIActionButton[]> {
    try {
      const actions: AIActionButton[] = []
      const messageLower = aiMessage.toLowerCase()

      // Authentication-based actions
      if (!userContext.is_authenticated) {
        if (messageLower.includes('sign in') || messageLower.includes('log in') || messageLower.includes('account')) {
          actions.push({
            id: 'auth-signin',
            label: 'Sign In',
            icon: 'login',
            color: 'primary',
            action_type: 'dialog',
            action_data: { dialog: 'auth-signin' },
            tooltip: 'Sign in to access all features'
          })
        }

        if (messageLower.includes('join') || messageLower.includes('register') || messageLower.includes('sign up')) {
          actions.push({
            id: 'auth-signup',
            label: 'Join Platform',
            icon: 'person_add',
            color: 'secondary',
            action_type: 'dialog',
            action_data: { dialog: 'auth-signup' },
            tooltip: 'Create your account'
          })
        }
      } else {
        // Authenticated user actions

        // Profile-related actions
        if (messageLower.includes('profile') || messageLower.includes('complete')) {
          const profileCompletion = userContext.profile_data?.profile_completion || 0
          if (profileCompletion < 80) {
            actions.push({
              id: 'complete-profile',
              label: 'Complete Profile',
              icon: 'person',
              color: 'orange',
              action_type: 'navigation',
              action_data: { route: '/dashboard', params: { section: 'profile' } },
              tooltip: 'Complete your profile for better visibility',
              requires_auth: true
            })
          }
        }

        // Navigation actions based on content
        if (messageLower.includes('dashboard') && userContext.current_page !== 'dashboard') {
          actions.push({
            id: 'go-dashboard',
            label: 'Go to Dashboard',
            icon: 'dashboard',
            color: 'primary',
            action_type: 'navigation',
            action_data: { route: '/dashboard' },
            tooltip: 'Access your personalized dashboard',
            requires_auth: true
          })
        }

        if (messageLower.includes('community') || messageLower.includes('feed') || messageLower.includes('connect')) {
          actions.push({
            id: 'explore-community',
            label: 'Explore Community',
            icon: 'people',
            color: 'secondary',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'feed' } },
            tooltip: 'Discover the innovation community'
          })
        }

        if (messageLower.includes('profile') || messageLower.includes('network') || messageLower.includes('connect')) {
          actions.push({
            id: 'browse-profiles',
            label: 'Browse Profiles',
            icon: 'people',
            color: 'blue',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'profiles' } },
            tooltip: 'Browse and connect with other innovators'
          })
        }

        if (messageLower.includes('marketplace') || messageLower.includes('opportunity') || messageLower.includes('collaboration')) {
          actions.push({
            id: 'explore-marketplace',
            label: 'Explore Marketplace',
            icon: 'store',
            color: 'teal',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'marketplace' } },
            tooltip: 'Find opportunities and collaborations'
          })
        }

        // Content creation actions
        if (messageLower.includes('post') || messageLower.includes('share') || messageLower.includes('create')) {
          actions.push({
            id: 'create-post',
            label: 'Create Post',
            icon: 'edit',
            color: 'green',
            action_type: 'dialog',
            action_data: { dialog: 'post-creation', params: { type: 'general' } },
            tooltip: 'Share your thoughts with the community',
            requires_auth: true
          })
        }

        // Help and support actions
        if (messageLower.includes('help') || messageLower.includes('support') || messageLower.includes('guide')) {
          actions.push({
            id: 'get-help',
            label: 'Get Help',
            icon: 'help',
            color: 'info',
            action_type: 'navigation',
            action_data: { route: '/help' },
            tooltip: 'Access help and support resources'
          })
        }

        // Quick follow-up actions based on conversation context
        if (messageLower.includes('project') || messageLower.includes('innovation')) {
          actions.push({
            id: 'discover-projects',
            label: 'Discover Projects',
            icon: 'lightbulb',
            color: 'amber',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'projects' } },
            tooltip: 'Explore innovative projects'
          })
        }

        if (messageLower.includes('event') || messageLower.includes('workshop') || messageLower.includes('meetup')) {
          actions.push({
            id: 'browse-events',
            label: 'Browse Events',
            icon: 'event',
            color: 'purple',
            action_type: 'navigation',
            action_data: { route: '/virtual-community', params: { tab: 'events' } },
            tooltip: 'Find upcoming events and workshops'
          })
        }
      }

      // Add general follow-up quick replies
      this.addQuickFollowUpActions(actions, messageLower, userContext)

      // Remove duplicates based on ID
      const uniqueActions = actions.filter((action, index, self) =>
        index === self.findIndex(a => a.id === action.id)
      )

      console.log('🎯 Generated contextual actions:', uniqueActions.map(a => a.label))
      return uniqueActions.slice(0, 4) // Limit to 4 actions to avoid UI clutter

    } catch (error) {
      console.error('❌ Error generating contextual action buttons:', error)
      return []
    }
  }

  /**
   * Add quick follow-up actions based on conversation context
   */
  private addQuickFollowUpActions(actions: AIActionButton[], messageLower: string, userContext: UserContext): void {
    // Add common follow-up questions as quick replies
    const followUpActions: AIActionButton[] = []

    if (userContext.is_authenticated) {
      // Authenticated user follow-ups
      if (messageLower.includes('recommend') || messageLower.includes('suggest')) {
        followUpActions.push({
          id: 'tell-me-more',
          label: 'Tell me more',
          icon: 'info',
          color: 'grey-7',
          action_type: 'trigger',
          action_data: { message: 'Can you provide more details about this?' },
          tooltip: 'Get more detailed information'
        })
      }

      if (messageLower.includes('connect') || messageLower.includes('network')) {
        followUpActions.push({
          id: 'how-to-connect',
          label: 'How to connect?',
          icon: 'help_outline',
          color: 'grey-7',
          action_type: 'trigger',
          action_data: { message: 'How can I connect with these people?' },
          tooltip: 'Learn about connection process'
        })
      }

      if (messageLower.includes('project') || messageLower.includes('opportunity')) {
        followUpActions.push({
          id: 'find-similar',
          label: 'Find similar',
          icon: 'search',
          color: 'grey-7',
          action_type: 'trigger',
          action_data: { message: 'Can you find similar projects or opportunities?' },
          tooltip: 'Discover similar content'
        })
      }
    } else {
      // Unauthenticated user follow-ups
      followUpActions.push({
        id: 'learn-more',
        label: 'Learn more',
        icon: 'school',
        color: 'grey-7',
        action_type: 'trigger',
        action_data: { message: 'Tell me more about the platform features' },
        tooltip: 'Learn about platform features'
      })
    }

    // Add follow-up actions to main actions array
    actions.push(...followUpActions.slice(0, 2)) // Limit follow-ups to avoid clutter
  }

  /**
   * Test AI service connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.sendMessage('Hello, this is a connection test')
      return response.success && !!response.message
    } catch (error) {
      console.error('AI service connection test failed:', error)
      return false
    }
  }
}

// Singleton instance
let _aiChatService: AIChatService | null = null

export const getAiChatService = (): AIChatService => {
  if (!_aiChatService) {
    _aiChatService = new AIChatService()
  }
  return _aiChatService
}

// Global test function for debugging
declare global {
  interface Window {
    __testAIChat: () => Promise<void>
  }
}

// Add global test function for debugging
window.__testAIChat = async () => {
  console.log('🧪 Testing AI Chat from global function...')
  try {
    const service = getAiChatService()
    const response = await service.sendMessage('Hello, can you tell me about the ZbInnovation platform?')
    console.log('✅ AI Chat test successful:', response)
  } catch (error) {
    console.error('❌ AI Chat test failed:', error)
  }
}

export default getAiChatService
