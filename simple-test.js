// Simple focused test for Edge Functions
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

// Helper function to make authenticated requests to Edge Functions
async function callEdgeFunction(functionName, body = {}, method = 'POST') {
  const headers = {
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'apikey': SUPABASE_ANON_KEY,
    'Content-Type': 'application/json',
  };

  const config = {
    method,
    headers
  };

  if (method !== 'GET' && body) {
    config.body = JSON.stringify(body);
  }

  console.log(`🔄 Calling ${functionName}...`);
  const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, config);
  
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    data: response.ok ? await response.json() : await response.text()
  };
}

async function testEmbeddingFunction() {
  console.log('🔢 Testing Embedding Function...');
  
  try {
    const result = await callEdgeFunction('embed', {
      input: "Test embedding for Zimbabwe innovation platform",
      model: "bge-m3",
      chunk_strategy: "none"
    });

    if (result.ok) {
      const data = result.data;
      console.log(`✅ Embedding Success:`);
      console.log(`   Model: ${data.model}`);
      console.log(`   Dimensions: ${data.dimensions}`);
      console.log(`   Processing time: ${data.processing_time_ms}ms`);
      console.log(`   Embedding length: ${data.embedding?.length || 'N/A'}`);
      return true;
    } else {
      console.log(`❌ Embedding Failed: ${result.status} - ${result.data}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Embedding Error: ${error.message}`);
    return false;
  }
}

async function testQueryRouter() {
  console.log('\n🧭 Testing Query Router...');
  
  try {
    const result = await callEdgeFunction('query-router', {
      query: "How many innovators are on the platform?"
    });

    if (result.ok) {
      const data = result.data;
      console.log(`✅ Router Success:`);
      console.log(`   Route: ${data.route}`);
      console.log(`   Confidence: ${(data.confidence * 100).toFixed(0)}%`);
      console.log(`   Reasoning: ${data.reasoning}`);
      return true;
    } else {
      console.log(`❌ Router Failed: ${result.status} - ${result.data}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Router Error: ${error.message}`);
    return false;
  }
}

async function testRAGPopulation() {
  console.log('\n📊 Testing RAG Population (Dry Run)...');
  
  try {
    const result = await callEdgeFunction('populate-rag-embeddings', {
      content_types: ['profile'],
      batch_size: 2,
      dry_run: true
    });

    if (result.ok) {
      const data = result.data;
      console.log(`✅ RAG Population Success:`);
      console.log(`   Content types: ${data.content_processed.length}`);
      data.content_processed.forEach(content => {
        console.log(`   ${content.content_type}: ${content.records_found} records found`);
      });
      console.log(`   Processing time: ${data.processing_time_ms}ms`);
      return true;
    } else {
      console.log(`❌ RAG Population Failed: ${result.status} - ${result.data}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ RAG Population Error: ${error.message}`);
    return false;
  }
}

async function testAIChat() {
  console.log('\n🤖 Testing AI Chat (Simple)...');
  
  try {
    const result = await callEdgeFunction('ai-chat', {
      message: "Hello, tell me about the platform",
      user_context: {},
      rag_enabled: false, // Disable RAG for simple test
      max_context_items: 0
    });

    if (result.ok) {
      const data = result.data;
      console.log(`✅ AI Chat Success:`);
      console.log(`   Route: ${data.query_route}`);
      console.log(`   Confidence: ${(data.route_confidence * 100).toFixed(0)}%`);
      console.log(`   Context items: ${data.rag_context_used?.length || 0}`);
      console.log(`   Processing time: ${data.processing_time_ms}ms`);
      console.log(`   Response preview: "${data.message?.substring(0, 100)}..."`);
      return true;
    } else {
      console.log(`❌ AI Chat Failed: ${result.status} - ${result.data}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ AI Chat Error: ${error.message}`);
    return false;
  }
}

// Run focused tests
async function runFocusedTests() {
  console.log('🚀 Starting Focused Edge Function Tests...\n');
  
  const results = {
    embedding: await testEmbeddingFunction(),
    router: await testQueryRouter(),
    ragPopulation: await testRAGPopulation(),
    aiChat: await testAIChat()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log(`   Embedding Function: ${results.embedding ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Query Router: ${results.router ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   RAG Population: ${results.ragPopulation ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   AI Chat: ${results.aiChat ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  console.log(`\n🎯 Overall: ${passCount}/4 tests passed (${(passCount/4*100).toFixed(0)}%)`);
  
  if (passCount === 4) {
    console.log('🎉 All systems operational!');
  } else {
    console.log('⚠️ Some systems need attention.');
  }
}

runFocusedTests();
