// Minimal RAG population test
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

async function callEdgeFunction(functionName, body = {}, method = 'POST') {
  const headers = {
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'apikey': SUPABASE_ANON_KEY,
    'Content-Type': 'application/json',
  };

  const config = { method, headers };
  if (method !== 'GET' && body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, config);
  
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    data: response.ok ? await response.json() : await response.text()
  };
}

async function testMinimalRAG() {
  console.log('🧪 Testing Minimal RAG Population...\n');
  
  // Test 1: Dry run first
  console.log('Step 1: Dry run test');
  try {
    const dryResult = await callEdgeFunction('populate-rag-embeddings', {
      content_types: ['profile'],
      batch_size: 1,
      dry_run: true
    });

    if (dryResult.ok) {
      console.log('✅ Dry run successful');
      console.log('Available content:', dryResult.data);
    } else {
      console.log('❌ Dry run failed:', dryResult.status, dryResult.data);
      return;
    }
  } catch (error) {
    console.log('❌ Dry run error:', error.message);
    return;
  }

  // Test 2: Try single embedding
  console.log('\nStep 2: Single embedding test');
  try {
    const singleResult = await callEdgeFunction('populate-rag-embeddings', {
      content_types: ['profile'],
      batch_size: 1,
      force_regenerate: false,
      dry_run: false
    });

    if (singleResult.ok) {
      console.log('✅ Single embedding successful');
      console.log('Result:', singleResult.data);
    } else {
      console.log('❌ Single embedding failed:', singleResult.status);
      console.log('Error:', singleResult.data);
    }
  } catch (error) {
    console.log('❌ Single embedding error:', error.message);
  }
}

async function testAIChatWithFixedEndpoint() {
  console.log('\n🤖 Testing AI Chat with Fixed Endpoint...\n');
  
  const testCases = [
    {
      message: "Hello! Tell me about the platform.",
      description: "Simple greeting"
    },
    {
      message: "How many users are on the platform?",
      description: "Analytics query"
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`Testing: ${testCase.description}`);
      console.log(`Message: "${testCase.message}"`);
      
      const result = await callEdgeFunction('ai-chat', {
        message: testCase.message,
        user_context: { user_id: null },
        rag_enabled: true,
        max_context_items: 3
      });

      if (result.ok) {
        const data = result.data;
        console.log(`✅ Success!`);
        console.log(`   Route: ${data.query_route}`);
        console.log(`   Processing time: ${data.processing_time_ms}ms`);
        
        // Check if it's a real response or fallback
        if (data.message && data.message.includes('technical difficulties')) {
          console.log(`   ⚠️ Fallback message`);
        } else {
          console.log(`   ✅ Real AI response!`);
          console.log(`   Response: "${data.message.substring(0, 100)}..."`);
        }
      } else {
        console.log(`❌ Failed: ${result.status} - ${result.data}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    
    console.log(''); // Add spacing
  }
}

async function runMinimalTests() {
  console.log('🚀 Running Minimal System Tests...\n');
  console.log('=' .repeat(50));
  
  // Test RAG population
  await testMinimalRAG();
  
  console.log('\n' + '=' .repeat(50));
  
  // Test AI chat with fixed endpoint
  await testAIChatWithFixedEndpoint();
  
  console.log('\n' + '=' .repeat(50));
  console.log('\n📋 Test Summary:');
  console.log('1. ✅ Claude API: Working (confirmed from previous test)');
  console.log('2. 🔧 RAG Embeddings: Testing minimal population');
  console.log('3. 🔧 Frontend Integration: Testing fixed endpoint');
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Test frontend with browser after these fixes');
  console.log('2. Verify auth dialogs work correctly');
  console.log('3. Check if real AI responses appear in frontend');
}

runMinimalTests();
