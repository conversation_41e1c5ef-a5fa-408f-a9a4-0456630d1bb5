import { defineStore } from 'pinia'
import { supabase } from '../lib/supabase'
import type { User } from '@supabase/supabase-js'
import router from '../router'
import { useNotificationStore } from '../stores/notifications'
import { ref, computed } from 'vue'
import { createPersonalDetails, checkPersonalDetailsTable } from '../lib/databaseSetup'
import { sendWelcomeEmail, sendPasswordResetEmail } from '../services/emailService'

/**
 * Updates personal details if they're missing required fields
 */
async function updatePersonalDetailsIfNeeded(userId: string, details: any): Promise<void> {
  try {
    // Check if any required fields are missing
    const requiredFields = {
      profile_name: 'My Profile',
      profile_state: 'IN_PROGRESS',
      profile_visibility: 'private',
      is_verified: false,
      profile_completion: 0
    }

    const updateData: Record<string, any> = {}
    let needsUpdate = false

    // Check each required field
    for (const [field, defaultValue] of Object.entries(requiredFields)) {
      if (details[field] === undefined || details[field] === null) {
        updateData[field] = defaultValue
        needsUpdate = true
      }
    }

    // If any fields are missing, update the record
    if (needsUpdate) {
      console.log('Updating personal details with missing fields:', updateData)
      const { error } = await supabase
        .from('personal_details')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (error) {
        console.error('Error updating personal details:', error.message)
      } else {
        console.log('Personal details updated successfully')
      }
    }
  } catch (error: any) {
    console.error('Error in updatePersonalDetailsIfNeeded:', error.message)
  }
}

// Authentication store with database integration
export const useAuthStore = defineStore('auth', () => {
  const notifications = useNotificationStore()

  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false)

  const isAuthenticated = computed(() => !!user.value)
  const currentUser = computed(() => user.value)

  // Simplified signup function that works without email verification
  async function signUp(userData: {
    email: string
    password: string
  }): Promise<{ user: User } | null> {
    loading.value = true
    error.value = null
    try {
      console.log('Starting simplified signup process...')

      // Basic validation
      if (!userData.email || !userData.password) {
        throw new Error('Email and password are required')
      }

      // Email validation with a more permissive regex
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        throw new Error('Please enter a valid email address')
      }

      // Password validation
      if (userData.password.length < 6) {
        throw new Error('Password must be at least 6 characters long')
      }

      // First, check if the user already exists
      try {
        const { data: existingUser, error: checkError } = await supabase.auth.signInWithPassword({
          email: userData.email,
          password: userData.password
        })

        if (existingUser?.user) {
          // User already exists and password is correct
          console.log('User already exists and password is correct, signing in')
          user.value = existingUser.user
          notifications.success('Successfully signed in!')
          router.push({ name: 'dashboard' })
          return { user: existingUser.user }
        }
      } catch (checkError) {
        // Ignore errors here, we're just checking if the user exists
        console.log('User does not exist or password is incorrect, proceeding with signup')
      }

      // Two-step approach: First create the user with admin API
      console.log('Attempting direct user creation...')

      // Create the user with regular signup
      console.log('Attempting regular signup...')
      const { data: regularData, error: regularError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          // Email confirmation is disabled in Supabase, but we'll still handle errors gracefully
          emailRedirectTo: `${window.location.origin}/auth/verify`
        }
      })

      // Handle specific error cases
      if (regularError) {
        // If it's not the email confirmation error, throw it
        if (!regularError.message.includes('confirmation email') &&
            !regularError.message.includes('unexpected_failure')) {
          console.error('Regular signup failed with unexpected error:', regularError)
          throw regularError
        }

        // If it's the email confirmation error, log it but continue
        console.warn('Email confirmation failed but continuing with signup:', regularError)

        // Try to sign in immediately since email confirmation is disabled
        try {
          const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
            email: userData.email,
            password: userData.password
          })

          if (!signInError && signInData.user) {
            console.log('Successfully signed in after email confirmation error')
            return { user: signInData.user }
          }
        } catch (signInError) {
          console.warn('Failed to sign in after email confirmation error:', signInError)
        }
      }

      // Check if we got a user back
      if (!regularData?.user) {
        throw new Error('No user data returned from signup')
      }

      // Use the regular signup data
      const data = { user: regularData.user }

      if (!data.user) {
        throw new Error('No user data returned from signup')
      }

      console.log('User created successfully:', data.user.id)

      // Set user state
      user.value = data.user

      // Step 2: Create personal details record
      console.log('Creating personal details record...')
      const createResult = await createPersonalDetails(data.user.id, data.user.email || '')
      if (!createResult.success) {
        console.warn('Personal details creation warning:', createResult.message)
      }

      // Step 3: Try to sign in the user immediately with multiple attempts
      try {
        console.log('Attempting to sign in user after signup...')
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: userData.email,
          password: userData.password
        })

        if (signInError) {
          console.warn('First sign-in attempt failed, trying again after delay:', signInError)

          // Wait a moment and try again
          await new Promise(resolve => setTimeout(resolve, 1500))

          // Second attempt
          const { data: retryData, error: retryError } = await supabase.auth.signInWithPassword({
            email: userData.email,
            password: userData.password
          })

          if (retryError) {
            console.warn('Second sign-in attempt also failed:', retryError)
            // We'll still continue with the flow
          } else if (retryData.user) {
            console.log('Successfully signed in user on second attempt')
            user.value = retryData.user
          }
        } else if (signInData.user) {
          console.log('Successfully signed in user after signup')
          user.value = signInData.user
        }
      } catch (signInError) {
        console.warn('Error during automatic sign in after signup:', signInError)
        // Don't throw error here, as the signup was successful
      }

      // Send welcome email
      try {
        console.log('Sending welcome email to:', data.user.email)
        const { sendWelcomeEmail } = await import('../services/emailService')
        const emailResult = await sendWelcomeEmail(data.user.email || '')

        if (emailResult.success) {
          console.log('Welcome email sent successfully')
          notifications.success('Welcome email sent successfully!')
        } else {
          console.error('Failed to send welcome email:', emailResult.error)
          notifications.warning('Account created successfully, but welcome email could not be sent.')
        }
      } catch (emailError) {
        console.error('Error sending welcome email:', emailError)
        console.error('Email error details:', JSON.stringify(emailError))
        notifications.warning('Account created successfully, but welcome email could not be sent.')
        // Don't throw error here, as the signup was successful
      }

      // Success message and redirect
      notifications.success('Registration successful! You can now use the platform.')

      // Redirect to dashboard if we have a session, otherwise to home
      if (user.value) {
        router.push({ name: 'dashboard' })
      } else {
        router.push({ name: 'home' })
      }

      // Return the user data
      return { user: data.user }

    } catch (error: any) {
      console.error('Signup error:', error)

      // Handle specific error messages
      if (error.message && error.message.includes('User already registered')) {
        notifications.error('An account with this email already exists. Please sign in instead.')
        router.push({ name: 'sign-in' })
      } else if (error.message && error.message.includes('Email rate limit exceeded')) {
        notifications.error('Too many signup attempts. Please try again later.')
      } else if (error.message && (error.message.includes('confirmation email') ||
                                  error.message.includes('unexpected_failure'))) {
        // For email confirmation errors, show a clear error message
        notifications.error('Registration failed. Please try again later or contact support.')
        return null;
      } else {
        notifications.error(error.message || 'Registration failed. Please try again.')
      }

      return null
    } finally {
      loading.value = false
    }
  }

  // Enhanced signin function with better error handling
  async function signIn(email: string, password: string): Promise<void> {
    loading.value = true
    error.value = null
    try {
      // Authenticate with Supabase
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      // Handle specific error cases
      if (signInError) {
        console.error('Sign in error:', signInError)

        // Check for email not confirmed error
        if (signInError.message && signInError.message.includes('Email not confirmed')) {
          // Show a clear error message
          throw new Error('Your email address has not been confirmed. Please contact support.')
        }

        throw signInError
      }

      if (!data.user) throw new Error('Sign in failed: No user data returned')

      // Set user state
      user.value = data.user

      // Clear session flags
      localStorage.removeItem('profileCompletionPopupShownThisSession')

      // Create personal details record if it doesn't exist (single query)
      const { data: personalDetails, error: personalDetailsError } = await supabase
        .from('personal_details')
        .select('user_id, email_verified')
        .eq('user_id', data.user.id)
        .maybeSingle()

      if (!personalDetails) {
        // Create minimal personal details record
        await supabase
          .from('personal_details')
          .insert({
            user_id: data.user.id,
            email: data.user.email || '',
            profile_state: 'DRAFT',
            profile_completion: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
      }

      notifications.success('Successfully signed in!')

      // Check if there's a redirect URL in the query parameters
      const route = router.currentRoute.value;
      if (route.query.redirect) {
        // Redirect to the specified URL
        const redirectUrl = route.query.redirect.toString();
        console.log('Redirecting to:', redirectUrl);
        window.location.href = redirectUrl;
      } else {
        // Default redirect to dashboard
        router.push({ name: 'dashboard' });
      }
    } catch (error: any) {
      error.value = error.message

      // Handle specific error messages to make them more user-friendly
      if (error.message && error.message.includes('Invalid login credentials')) {
        notifications.error('Invalid email or password. Please try again.')
      } else if (error.message && error.message.includes('User already registered')) {
        // This error shouldn't happen during sign-in, but if it does, provide a clearer message
        notifications.error('Please use the sign-in form to log in with your existing account.')
      } else {
        notifications.error(error.message || 'Failed to sign in. Please check your credentials.')
      }
    } finally {
      loading.value = false
    }
  }

  // Sign out function
  async function signOut(): Promise<void> {
    try {
      await supabase.auth.signOut()
      user.value = null

      // Clear the profile completion popup session flag
      localStorage.removeItem('profileCompletionPopupShownThisSession')
      localStorage.removeItem('profileCompletionRemindLater')

      // Import and reset the profile store
      const { useProfileStore } = await import('../stores/profile')
      const profileStore = useProfileStore()
      profileStore.reset()

      router.push('/')
      notifications.success('Successfully signed out')
    } catch (error: any) {
      notifications.error(error.message || 'Failed to sign out')
    }
  }

  // Password reset function
  async function resetPassword(email: string): Promise<void> {
    loading.value = true
    error.value = null
    try {
      // Basic validation
      if (!email) {
        throw new Error('Email is required')
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address')
      }

      // Ensure we have the correct site URL
      const siteUrl = window.location.origin
      const redirectUrl = `${siteUrl}/reset-password-confirm`

      console.log('Attempting password reset with:', {
        email,
        siteUrl,
        redirectUrl
      })

      // Check if the user exists in our database
      const { data: userData, error: userError } = await supabase
        .from('personal_details')
        .select('first_name')
        .eq('email', email)
        .maybeSingle()

      // Get the first name if available
      const firstName = userData?.first_name

      // Generate a token for the password reset
      // This is a simplified approach - in production, you'd want to use a more secure method
      const timestamp = Date.now()
      const token = btoa(`${email}:${timestamp}`)

      // Create the reset link with the token
      const resetLink = `${redirectUrl}?token=${encodeURIComponent(token)}&type=recovery&email=${encodeURIComponent(email)}`
      console.log('Generated reset link:', resetLink)

      // Use Supabase Auth's built-in password reset
      const { data, error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl
      })

      console.log('Supabase password reset request sent:', {
        email,
        redirectUrl,
        response: data,
        error: resetError
      })

      if (resetError) {
        console.error('Password reset error:', resetError)
        // For security reasons, we don't want to reveal if an email exists or not
        // So we'll still show a success message even if there was an error
      }

      // Success message
      notifications.success('If an account exists with this email, password reset instructions will be sent.')
    } catch (error: any) {
      console.error('Password reset error:', error)
      error.value = error.message

      // For UI purposes, we'll still show a success message for security
      // But we'll log the actual error for debugging
      if (error.message.includes('service not configured') ||
          error.message.includes('email provider') ||
          error.message.includes('smtp') ||
          error.message.includes('Error sending recovery email') ||
          error.message.includes('AuthApiError')) {
        // This is a server configuration error, not a user error
        console.error('Email service configuration error:', error.message)
        throw new Error('Error sending recovery email')
      } else {
        // For other errors, we'll still show a generic success message for security
        notifications.success('If an account exists with this email, password reset instructions will be sent.')
      }
    } finally {
      loading.value = false
    }
  }

  // Simplified session check
  async function checkSession(): Promise<void> {
    try {
      loading.value = true
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        user.value = session.user
      } else {
        user.value = null
      }

      // Set up auth state change listener
      supabase.auth.onAuthStateChange((event, session) => {
        if (session) {
          user.value = session.user
        } else {
          user.value = null
        }
      })
    } catch (err) {
      console.error('Session check error:', err)
    } finally {
      isInitialized.value = true
      loading.value = false
    }
  }

  return {
    user,
    loading,
    error,
    isInitialized,
    isAuthenticated,
    currentUser,
    signUp,
    signIn,
    signOut,
    resetPassword,
    checkSession
  }
})

