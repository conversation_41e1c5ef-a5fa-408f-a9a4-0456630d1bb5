<template>
  <div class="early-access-form">
    <!-- Different content based on authentication status -->
    <template v-if="!isAuthenticated">
      <!-- Auth Options -->
      <auth-options
        mode="signup"
        @email-password-signup="showEmailPasswordForm = true"
      />

      <!-- Email Password Form -->
      <q-dialog v-model="showEmailPasswordForm" persistent>
        <q-card style="min-width: 350px">
          <q-card-section>
            <div class="text-h6">Sign Up with Email</div>
          </q-card-section>

          <q-card-section class="q-gutter-md">
            <q-input
              v-model="form.email"
              type="email"
              label="Email"
              outlined
              :rules="emailRules"
            >
              <template v-slot:prepend>
                <unified-icon name="mail" />
              </template>
            </q-input>

            <q-input
              v-model="form.password"
              :type="isPwd ? 'password' : 'text'"
              label="Password"
              outlined
              :rules="passwordRules"
            >
              <template v-slot:prepend>
                <unified-icon name="lock" />
              </template>
              <template v-slot:append>
                <div class="cursor-pointer" @click="isPwd = !isPwd">
                  <unified-icon :name="isPwd ? 'eye-off' : 'eye'" />
                </div>
              </template>
            </q-input>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="Cancel" color="primary" v-close-popup :disable="loading" />
            <q-btn
              flat
              label="Submit"
              color="primary"
              @click="handleEmailPasswordSignup"
              :loading="loading"
              :disable="loading"
            />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </template>

    <!-- Message for logged-in users -->
    <template v-else>
      <div class="text-center q-pa-md">
        <template v-if="profileCompletion < 100">
          <p class="text-body1 q-mb-md">Complete your profile to enhance matchmaking with other innovators, gain personalized recommendations, and maximize your networking opportunities within our ecosystem.</p>
          <q-btn
            color="primary"
            label="Complete Your Profile"
            class="q-mt-md cta-button"
            @click="goToProfileEdit"
            size="md"
            rounded
          />
        </template>
        <template v-else>
          <p class="text-body1 q-mb-md">Your complete profile helps us connect you with the right partners, resources, and opportunities. Explore the dashboard to start networking and collaborating with other ecosystem members.</p>
          <q-btn
            color="primary"
            label="Go to Dashboard"
            class="q-mt-md"
            @click="goToDashboard"
            size="lg"
          />
        </template>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '../../stores/notifications'
import { useProfileStore } from '../../stores/profile'
import AuthOptions from '../auth/AuthOptions.vue'
// import { supabase } from '../../lib/supabase' // Not used
import UnifiedIcon from '../ui/UnifiedIcon.vue'

const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notificationStore = useNotificationStore()
const isPwd = ref(true)
const loading = ref(false)
const showEmailPasswordForm = ref(false)

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated)

// Get profile completion percentage
const profileCompletion = computed(() => profileStore.profileCompletion)

// Navigation functions
const goToDashboard = () => {
  router.push('/dashboard')
}

const goToProfileEdit = () => {
  router.push('/dashboard/profile')
}

// Form data for signup
const form = ref({
  email: '',
  password: ''
})

// Validation rules
const emailRules = [
  (val: string) => !!val || 'Email is required',
  (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
]

const passwordRules = [
  (val: string) => !!val || 'Password is required',
  (val: string) => val.length >= 6 || 'Password must be at least 6 characters'
]

// Handle email/password signup
const handleEmailPasswordSignup = async () => {
  try {
    loading.value = true

    // Basic validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!form.value.email || !emailRegex.test(form.value.email)) {
      throw new Error('Please enter a valid email address')
    }

    // Simplified password validation
    if (form.value.password.length < 6) {
      throw new Error('Password must be at least 6 characters')
    }

    console.log('Preparing to sign up with:', { email: form.value.email })

    // Use auth store for signup (handles success notification and redirect)
    await authStore.signUp({
      email: form.value.email,
      password: form.value.password
    })

    // Close the dialog
    showEmailPasswordForm.value = false

    // Don't show a duplicate success message - the auth store already shows one
    // This prevents the double notification issue

    // Redirect to dashboard
    router.push('/dashboard')

  } catch (error: any) {
    console.error('Email password signup error:', error)
    // Handle specific error messages to make them more user-friendly
    if (error.message && error.message.includes('User already registered')) {
      // If the user is already registered, suggest they sign in instead
      notificationStore.error('This email is already registered. Please use the Sign In option instead.')
    } else {
      notificationStore.error(error.message || 'Registration failed. Please try again.')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Base styles */
.early-access-form {
  width: 100%;
}

/* Form element styles */
.q-form {
  gap: 1rem !important;
}

.custom-btn {
  min-width: 200px;
}

.cta-button {
  border-radius: 25px !important;
  min-width: 160px;
  height: 40px;
  font-weight: 600;
}

.icon {
  font-size: 20px;
  display: flex;
  align-items: center;
}

.icon-arrow-down {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

/* Deep selectors for Quasar components */
:deep(.q-field) {
  width: 100%;
}

:deep(.q-input), :deep(.q-select) {
  width: 100%;
}

:deep(.q-field__control) {
  width: 100%;
}

:deep(.row) {
  margin: 0;
}

:deep(.col-sm-12) {
  padding: 0;
}

:deep(.q-field--focused) .icon-arrow-down {
  transform: rotate(180deg);
}

:deep(.q-select .q-field__append) {
  padding-right: 8px;
  height: 100%;
  display: flex;
  align-items: center;
}

:deep(.q-select .q-field__append .q-icon) {
  display: none;
}

:deep(.q-select .q-field__append .icon) {
  opacity: 0.7;
}

:deep(.q-field--focused .q-field__append .icon) {
  opacity: 1;
}

/* SVG icon styling */
:deep(svg) {
  width: 20px;
  height: 20px;
  stroke: currentColor;
}

:deep(.q-field__control-item svg) {
  color: #0D8A3E;
}

/* Responsive styles */
@media (max-width: 599px) {
  .early-access-form {
    padding: 0;
  }

  .custom-btn {
    width: 100%;
  }
}
</style>
