<template>
  <div class="profile-completion-status">
    <q-card class="q-mb-md profile-completion-card">
      <q-card-section>
        <div class="row justify-between items-center">
          <div class="text-h6">Profile Completion</div>
          <div class="text-subtitle1" :class="completionTextClass">
            {{ completionStatusText }}
          </div>
        </div>

        <!-- Progress bar -->
        <q-linear-progress
          :value="completionRatio"
          :color="completionColor"
          class="q-mt-sm"
          size="10px"
        />

        <!-- Step indicator -->
        <div class="row justify-between items-center q-mt-sm">
          <div class="text-subtitle2">{{ completedSteps }}/3 steps</div>
          <div class="row q-gutter-sm profile-action-buttons">
            <q-btn
              v-if="showViewButton"
              color="green-9"
              label="View Profile"
              :to="{ name: 'profile-view', params: { id: profileId } }"
              outline
              icon="visibility"
              size="md"
              rounded
              class="text-weight-medium cta-button"
            />
            <q-btn
              color="green-9"
              label="Complete Profile"
              :to="{ name: 'profile-edit', params: { id: profileId } }"
              icon="edit"
              size="md"
              rounded
              class="text-weight-medium cta-button"
            />
          </div>
        </div>

        <!-- Completion details -->
        <div class="q-mt-md completion-details">
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-4">
              <q-item class="rounded-borders" :class="{'bg-green-1': isStep1Complete}">
                <q-item-section avatar>
                  <q-avatar :color="isStep1Complete ? 'green-9' : 'grey-6'" text-color="white" icon="person" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Basic Information</q-item-label>
                  <q-item-label caption>
                    {{ isStep1Complete ? 'Completed' : 'Incomplete' }}
                  </q-item-label>
                </q-item-section>
                <q-item-section side v-if="isStep1Complete">
                  <q-icon name="check_circle" color="green-9" />
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-4">
              <q-item class="rounded-borders" :class="{'bg-green-1': isStep2Complete}">
                <q-item-section avatar>
                  <q-avatar :color="isStep2Complete ? 'green-9' : 'grey-6'" text-color="white" icon="category" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Profile Category</q-item-label>
                  <q-item-label caption>
                    {{ isStep2Complete ? profileType ? formatProfileType(profileType) : 'Selected' : 'Not selected' }}
                  </q-item-label>
                </q-item-section>
                <q-item-section side v-if="isStep2Complete">
                  <q-icon name="check_circle" color="green-9" />
                </q-item-section>
              </q-item>
            </div>

            <div class="col-12 col-md-4">
              <q-item class="rounded-borders" :class="{'bg-green-1': isStep3Complete}">
                <q-item-section avatar>
                  <q-avatar :color="isStep3Complete ? 'green-9' : 'grey-6'" text-color="white" icon="assignment" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Specialized Information</q-item-label>
                  <q-item-label caption>
                    {{ isStep3Complete ? 'All details provided' : missingFieldsText }}
                  </q-item-label>
                </q-item-section>
                <q-item-section side v-if="isStep3Complete">
                  <q-icon name="check_circle" color="green-9" />
                </q-item-section>
              </q-item>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useProfileStore } from '../../stores/profile'
import { formatProfileType } from '../../services/profileTypes'

const props = defineProps({
  profile: {
    type: Object,
    required: true
  },
  showViewButton: {
    type: Boolean,
    default: true
  },
  compact: {
    type: Boolean,
    default: false
  }
})

const profileStore = useProfileStore()

// Computed properties for profile completion status
const profileId = computed(() => props.profile?.user_id || '')
const profileType = computed(() => props.profile?.profile_type || '')
const profileCompletion = computed(() => props.profile?.profile_completion || 0)

// Step completion status
const isStep1Complete = computed(() => {
  return !!(props.profile?.first_name && props.profile?.last_name && props.profile?.email)
})

const isStep2Complete = computed(() => {
  return !!props.profile?.profile_type
})

const isStep3Complete = computed(() => {
  return profileCompletion.value >= 100
})

// Count completed steps
const completedSteps = computed(() => {
  let count = 0
  if (isStep1Complete.value) count++
  if (isStep2Complete.value) count++
  if (isStep3Complete.value) count++
  return count
})

// Completion ratio for progress bar
const completionRatio = computed(() => {
  return completedSteps.value / 3
})

// Text and styling for completion status
const completionStatusText = computed(() => {
  if (completedSteps.value === 3) return 'Profile Complete'
  if (completedSteps.value === 0) return 'Not Started'
  return 'Profile Incomplete'
})

const completionTextClass = computed(() => {
  if (completedSteps.value === 3) return 'text-positive'
  if (completedSteps.value === 0) return 'text-negative'
  return 'text-green-9'
})

const completionColor = computed(() => {
  if (completedSteps.value === 3) return 'positive'
  return 'green-9'
})

// Text for missing fields in step 3
const missingFieldsText = computed(() => {
  if (!isStep2Complete.value) return 'Select profile type first'

  // This would ideally check specific fields based on profile type
  // For now, we'll use a generic message
  return 'Some details missing'
})
</script>

<style scoped>
.profile-completion-card {
  border-left: 4px solid #0D8A3E; /* Green for completion */
}

.completion-details {
  transition: all 0.3s ease;
}

.cta-button {
  border-radius: 25px !important;
  min-width: 160px;
  height: 40px;
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .completion-details .row {
    flex-direction: column;
  }

  .profile-action-buttons {
    width: 100%;
    margin-top: 12px;
  }

  .profile-action-buttons .q-btn {
    flex: 1;
  }

  .row.justify-between.items-center {
    flex-direction: column;
    align-items: flex-start !important;
  }
}
</style>
