// Test Claude API implementation
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

// Helper function to make authenticated requests to Edge Functions
async function callEdgeFunction(functionName, body = {}, method = 'POST') {
  const headers = {
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'apikey': SUPABASE_ANON_KEY,
    'Content-Type': 'application/json',
  };

  const config = {
    method,
    headers
  };

  if (method !== 'GET' && body) {
    config.body = JSON.stringify(body);
  }

  console.log(`🔄 Calling ${functionName}...`);
  const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, config);
  
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    data: response.ok ? await response.json() : await response.text()
  };
}

async function testClaudeAIChat() {
  console.log('🤖 Testing Claude AI Chat...');
  
  const testCases = [
    {
      message: "Hello! Tell me about innovation opportunities in Zimbabwe.",
      description: "Simple greeting and platform question",
      expectedRoute: "rag"
    },
    {
      message: "How many innovators are registered on the platform?",
      description: "Analytics question",
      expectedRoute: "text2sql"
    },
    {
      message: "Find me mentors with AI expertise and show their success metrics.",
      description: "Complex hybrid query",
      expectedRoute: "hybrid"
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n💬 Testing: ${testCase.description}`);
      console.log(`   Query: "${testCase.message}"`);
      
      const result = await callEdgeFunction('ai-chat', {
        message: testCase.message,
        user_context: { user_id: null },
        rag_enabled: true,
        max_context_items: 5
      });

      if (result.ok) {
        const data = result.data;
        console.log(`✅ Success!`);
        console.log(`   Route: ${data.query_route} (expected: ${testCase.expectedRoute})`);
        console.log(`   Confidence: ${(data.route_confidence * 100).toFixed(0)}%`);
        console.log(`   Context items: ${data.rag_context_used?.length || 0}`);
        console.log(`   Processing time: ${data.processing_time_ms}ms`);
        
        // Check if it's a real Claude response or fallback
        if (data.message && data.message.includes('technical difficulties')) {
          console.log(`   ⚠️ Using fallback message - Claude API not working`);
        } else {
          console.log(`   ✅ Real AI response received!`);
          console.log(`   Response preview: "${data.message.substring(0, 150)}..."`);
        }
        
        if (data.rag_context_used?.length > 0) {
          console.log(`   Context sources: ${data.rag_context_used.map(c => c.source).join(', ')}`);
        }
      } else {
        console.log(`❌ AI Chat failed: ${result.status} - ${result.data}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

async function testClaudeAPIDirectly() {
  console.log('\n🔑 Testing Claude API directly (if possible)...');
  
  // Note: We can't test directly from client-side due to CORS and API key security
  // But we can test via the Edge Function which should have the API key
  
  try {
    const result = await callEdgeFunction('ai-chat', {
      message: "Just say 'Hello from Claude API' - this is a direct test",
      user_context: {},
      rag_enabled: false, // Disable RAG to focus on Claude API
      max_context_items: 0
    });

    if (result.ok) {
      const data = result.data;
      console.log(`✅ Claude API Test via Edge Function:`);
      console.log(`   Success: ${data.success}`);
      console.log(`   Processing time: ${data.processing_time_ms}ms`);
      console.log(`   Message: "${data.message}"`);
      
      // Analyze the response to determine if Claude API is working
      if (data.message && data.message.includes('technical difficulties')) {
        console.log(`\n❌ Claude API is NOT working - using fallback`);
        return false;
      } else if (data.message && data.message.toLowerCase().includes('hello')) {
        console.log(`\n✅ Claude API is WORKING - real response received!`);
        return true;
      } else {
        console.log(`\n🤔 Unclear if Claude API is working - unexpected response`);
        return false;
      }
    } else {
      console.log(`❌ Edge Function failed: ${result.status} - ${result.data}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return false;
  }
}

// Run comprehensive Claude tests
async function runClaudeTests() {
  console.log('🚀 Starting Claude API Tests...\n');
  console.log('=' .repeat(60));
  
  // Test Claude API functionality
  const claudeWorking = await testClaudeAPIDirectly();
  
  console.log('\n' + '=' .repeat(60));
  
  // Test AI Chat with Claude
  await testClaudeAIChat();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Claude API Test Summary:');
  console.log(`   Claude API Status: ${claudeWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);
  console.log(`   AI Chat System: ✅ FUNCTIONAL (with fallback)`);
  
  if (claudeWorking) {
    console.log('\n🎉 Claude API is working! The AI system is fully operational.');
    console.log('💡 Benefits of Claude API:');
    console.log('   - High-quality responses');
    console.log('   - Better context understanding');
    console.log('   - More reliable than DeepSeek');
    console.log('   - Excellent for innovation platform use cases');
  } else {
    console.log('\n⚠️ Claude API is not working, but system has graceful fallback.');
    console.log('💡 Possible issues:');
    console.log('   - Claude API key not set in Supabase environment');
    console.log('   - Invalid or expired Claude API key');
    console.log('   - Insufficient Claude API credits');
    console.log('   - Network connectivity issues');
    console.log('   - Rate limiting');
  }
  
  console.log('\n🎯 Next Steps:');
  if (claudeWorking) {
    console.log('   ✅ System is ready for production use!');
    console.log('   ✅ All components working with Claude API');
  } else {
    console.log('   🔧 Check Claude API key in Supabase dashboard');
    console.log('   🔧 Verify Claude API credits and account status');
    console.log('   🔧 Test API key directly on Claude console');
  }
}

runClaudeTests();
