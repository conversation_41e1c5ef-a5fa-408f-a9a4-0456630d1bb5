// Simple test for DeepSeek API
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

async function testDeepSeekViaEdgeFunction() {
  console.log('🤖 Testing DeepSeek API via AI Chat Edge Function...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: "Just say 'Hello from DeepSeek' - this is a test",
        user_context: {},
        rag_enabled: false, // Disable RAG to focus on DeepSeek API
        max_context_items: 0
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ AI Chat Response received:`);
      console.log(`   Success: ${data.success}`);
      console.log(`   Route: ${data.query_route}`);
      console.log(`   Processing time: ${data.processing_time_ms}ms`);
      console.log(`   Message: "${data.message}"`);
      
      // Check if it's a fallback message or actual DeepSeek response
      if (data.message && data.message.includes('technical difficulties')) {
        console.log(`\n⚠️ Using fallback message - DeepSeek API not working`);
        return false;
      } else if (data.message && data.message.toLowerCase().includes('hello')) {
        console.log(`\n✅ Likely DeepSeek API response - working!`);
        return true;
      } else {
        console.log(`\n🤔 Unclear if DeepSeek or fallback`);
        return false;
      }
    } else {
      const errorText = await response.text();
      console.log(`❌ AI Chat failed: ${response.status} - ${errorText}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return false;
  }
}

async function testDeepSeekDirectly() {
  console.log('\n🔑 Testing DeepSeek API directly...');
  
  // Test the API key that should be in the environment
  const apiKey = 'sk-df7def28182b4062be4d0fb793d3fcb9';
  
  try {
    const response = await fetch('https://api.deepseek.com/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'user', content: 'Say "Hello from DeepSeek API test"' }
        ],
        max_tokens: 20
      })
    });

    console.log(`📊 Direct API Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Direct API Success!`);
      console.log(`   Response: "${data.choices[0].message.content}"`);
      return true;
    } else {
      const errorText = await response.text();
      console.log(`❌ Direct API Error: ${errorText}`);
      
      // Parse error for specific issues
      try {
        const errorData = JSON.parse(errorText);
        if (errorData.error && errorData.error.code === 401) {
          console.log(`🔑 Authentication failed - API key issue`);
        } else if (errorData.error && errorData.error.code === 402) {
          console.log(`💰 Insufficient balance`);
        } else if (errorData.error && errorData.error.code === 429) {
          console.log(`⏱️ Rate limit reached`);
        }
      } catch (e) {
        // Not JSON error
      }
      
      return false;
    }
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
    return false;
  }
}

// Run tests
async function runDeepSeekDiagnostics() {
  console.log('🚀 DeepSeek API Diagnostics...\n');
  
  const directTest = await testDeepSeekDirectly();
  const edgeFunctionTest = await testDeepSeekViaEdgeFunction();
  
  console.log('\n📊 DeepSeek API Status Summary:');
  console.log(`   Direct API Test: ${directTest ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`   Via Edge Function: ${edgeFunctionTest ? '✅ WORKING' : '❌ FAILED'}`);
  
  if (directTest && edgeFunctionTest) {
    console.log('\n🎉 DeepSeek API is fully functional!');
  } else if (directTest && !edgeFunctionTest) {
    console.log('\n⚠️ DeepSeek API works directly but not via Edge Function');
    console.log('💡 Possible issues:');
    console.log('   - Environment variable not set in Supabase');
    console.log('   - Edge Function timeout');
    console.log('   - Network restrictions in Edge Function environment');
  } else if (!directTest && !edgeFunctionTest) {
    console.log('\n❌ DeepSeek API is not working');
    console.log('💡 Possible issues:');
    console.log('   - Invalid API key');
    console.log('   - Insufficient balance');
    console.log('   - Rate limit exceeded');
    console.log('   - Network connectivity issues');
  } else {
    console.log('\n🤔 Inconsistent results - needs further investigation');
  }
}

runDeepSeekDiagnostics();
