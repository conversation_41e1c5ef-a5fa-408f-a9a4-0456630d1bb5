// Test DeepSeek API directly
const DEEPSEEK_API_KEY = '***********************************';
const DEEPSEEK_BASE_URL = 'https://api.deepseek.com';

async function testDeepSeekAPI() {
  console.log('🤖 Testing DeepSeek API directly...');
  console.log(`API Key: ${DEEPSEEK_API_KEY.substring(0, 10)}...`);
  console.log(`Base URL: ${DEEPSEEK_BASE_URL}`);
  
  try {
    const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful AI assistant for ZbInnovation platform.'
          },
          {
            role: 'user',
            content: 'Hello, tell me about innovation in Zimbabwe.'
          }
        ],
        temperature: 0.7,
        max_tokens: 100
      }),
    });

    console.log(`\n📊 Response Status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Error Response: ${errorText}`);
      return false;
    }

    const data = await response.json();
    console.log(`✅ Success! Response received:`);
    console.log(`   Model: ${data.model || 'N/A'}`);
    console.log(`   Usage: ${JSON.stringify(data.usage || {})}`);
    
    if (data.choices && data.choices[0] && data.choices[0].message) {
      console.log(`   Response: "${data.choices[0].message.content.substring(0, 100)}..."`);
      return true;
    } else {
      console.log(`❌ Invalid response format: ${JSON.stringify(data)}`);
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
    return false;
  }
}

async function testDeepSeekModels() {
  console.log('\n🔍 Testing DeepSeek Models endpoint...');
  
  try {
    const response = await fetch(`${DEEPSEEK_BASE_URL}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      }
    });

    console.log(`📊 Models Response Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Available models: ${data.data?.length || 0}`);
      if (data.data && data.data.length > 0) {
        data.data.slice(0, 3).forEach(model => {
          console.log(`   - ${model.id}`);
        });
      }
      return true;
    } else {
      const errorText = await response.text();
      console.log(`❌ Models Error: ${errorText}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Models Network Error: ${error.message}`);
    return false;
  }
}

async function testAlternativeAPIKey() {
  console.log('\n🔑 Testing with alternative API key format...');
  
  // Test if the API key needs different formatting
  const alternativeKeys = [
    '***********************************', // Original
    '***********************************'.trim(), // Trimmed
  ];
  
  for (const apiKey of alternativeKeys) {
    try {
      console.log(`\n🔄 Testing key: ${apiKey.substring(0, 10)}...`);
      
      const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: 'Hello' }
          ],
          max_tokens: 10
        }),
      });

      console.log(`   Status: ${response.status}`);
      
      if (response.ok) {
        console.log(`   ✅ This key works!`);
        return true;
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText.substring(0, 100)}`);
      }
    } catch (error) {
      console.log(`   ❌ Network error: ${error.message}`);
    }
  }
  
  return false;
}

async function testAPIConnectivity() {
  console.log('\n🌐 Testing basic connectivity to DeepSeek...');
  
  try {
    // Test basic connectivity
    const response = await fetch(DEEPSEEK_BASE_URL, {
      method: 'GET'
    });
    
    console.log(`📊 Base URL Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 404 || response.status === 200) {
      console.log(`✅ DeepSeek server is reachable`);
      return true;
    } else {
      console.log(`⚠️ Unexpected response from DeepSeek server`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Cannot reach DeepSeek server: ${error.message}`);
    return false;
  }
}

// Run comprehensive DeepSeek tests
async function runDeepSeekTests() {
  console.log('🚀 Starting DeepSeek API Comprehensive Tests...\n');
  console.log('=' .repeat(60));
  
  const results = {
    connectivity: await testAPIConnectivity(),
    models: await testDeepSeekModels(),
    chat: await testDeepSeekAPI(),
    alternatives: await testAlternativeAPIKey()
  };
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 DeepSeek API Test Results:');
  console.log(`   Connectivity: ${results.connectivity ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Models Endpoint: ${results.models ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Chat Completions: ${results.chat ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Alternative Keys: ${results.alternatives ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  console.log(`\n🎯 Overall: ${passCount}/4 DeepSeek tests passed (${(passCount/4*100).toFixed(0)}%)`);
  
  if (results.chat) {
    console.log('\n🎉 DeepSeek API is working! The fallback in AI Chat might be due to other issues.');
  } else if (results.connectivity) {
    console.log('\n⚠️ DeepSeek server is reachable but API authentication is failing.');
    console.log('💡 Possible issues:');
    console.log('   - API key might be invalid or expired');
    console.log('   - API key might need different formatting');
    console.log('   - Rate limiting or quota exceeded');
    console.log('   - Different endpoint URL required');
  } else {
    console.log('\n❌ Cannot reach DeepSeek servers. Possible issues:');
    console.log('   - Network connectivity problems');
    console.log('   - Firewall blocking requests');
    console.log('   - DeepSeek service temporarily down');
    console.log('   - Incorrect base URL');
  }
}

runDeepSeekTests();
