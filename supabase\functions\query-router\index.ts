import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Types for query routing
interface QueryRouterRequest {
  query: string;
  user_context?: {
    user_id?: string;
    profile_type?: string;
    profile_status?: string;
  };
  force_route?: 'rag' | 'text2sql' | 'hybrid';
}

interface QueryRouterResponse {
  success: boolean;
  route: 'rag' | 'text2sql' | 'hybrid';
  confidence: number;
  reasoning: string;
  suggested_approach: {
    primary_method: string;
    fallback_method?: string;
    context_needed: string[];
  };
  processing_time_ms: number;
  error?: string;
}

// DeepSeek API configuration for lightweight classification
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY');
const DEEPSEEK_BASE_URL = 'https://api.deepseek.com';

/**
 * Pattern-based query classification (fast, no API calls)
 */
const QUERY_PATTERNS = {
  rag: [
    // Content and semantic queries
    /tell me about|describe|explain|what is|who are|find me|show me profiles/i,
    /innovation|startup|mentor|investor|entrepreneur/i,
    /opportunities|collaboration|matching|connect me/i,
    /similar to|like|related to|comparable/i,
    /experience|background|expertise|skills/i,
    /help me|advice|guidance|recommend/i,
    /bio|profile|story|journey/i
  ],
  text2sql: [
    // Analytics and data queries
    /how many|count|total|number of|statistics/i,
    /last month|this year|between|since|during/i,
    /top \d+|ranking|metrics|performance|analytics/i,
    /average|median|sum|maximum|minimum/i,
    /growth|trend|increase|decrease|percentage/i,
    /compare numbers|data analysis|report/i,
    /dashboard|chart|graph|visualization/i
  ],
  hybrid: [
    // Complex queries needing both approaches
    /show me.*and.*data|find.*with.*statistics/i,
    /profiles.*metrics|users.*analytics/i,
    /top.*profiles|best.*with.*numbers/i,
    /compare.*and.*show|analyze.*profiles/i,
    /recommend.*based on.*data/i
  ]
};

/**
 * Advanced query classification using patterns and keywords
 */
function classifyQueryByPatterns(query: string): { route: string; confidence: number; reasoning: string } {
  const lowerQuery = query.toLowerCase();
  
  // Check for explicit hybrid patterns first
  for (const pattern of QUERY_PATTERNS.hybrid) {
    if (pattern.test(lowerQuery)) {
      return {
        route: 'hybrid',
        confidence: 0.85,
        reasoning: 'Query contains both content and analytical elements'
      };
    }
  }
  
  // Check for text2sql patterns
  let sqlScore = 0;
  for (const pattern of QUERY_PATTERNS.text2sql) {
    if (pattern.test(lowerQuery)) {
      sqlScore += 1;
    }
  }
  
  // Check for RAG patterns
  let ragScore = 0;
  for (const pattern of QUERY_PATTERNS.rag) {
    if (pattern.test(lowerQuery)) {
      ragScore += 1;
    }
  }
  
  // Keyword-based scoring
  const analyticsKeywords = ['count', 'total', 'how many', 'statistics', 'metrics', 'data', 'analytics'];
  const contentKeywords = ['tell me', 'describe', 'find', 'show', 'help', 'recommend', 'similar'];
  
  analyticsKeywords.forEach(keyword => {
    if (lowerQuery.includes(keyword)) sqlScore += 0.5;
  });
  
  contentKeywords.forEach(keyword => {
    if (lowerQuery.includes(keyword)) ragScore += 0.5;
  });
  
  // Determine route based on scores
  if (sqlScore > ragScore && sqlScore >= 1) {
    return {
      route: 'text2sql',
      confidence: Math.min(0.9, 0.6 + (sqlScore * 0.1)),
      reasoning: `Analytics query detected (score: ${sqlScore} vs ${ragScore})`
    };
  } else if (ragScore > sqlScore && ragScore >= 1) {
    return {
      route: 'rag',
      confidence: Math.min(0.9, 0.6 + (ragScore * 0.1)),
      reasoning: `Content/semantic query detected (score: ${ragScore} vs ${sqlScore})`
    };
  } else if (sqlScore > 0 && ragScore > 0) {
    return {
      route: 'hybrid',
      confidence: 0.7,
      reasoning: 'Mixed query with both content and analytical elements'
    };
  } else {
    // Default to RAG for general queries
    return {
      route: 'rag',
      confidence: 0.5,
      reasoning: 'General query, defaulting to content search'
    };
  }
}

/**
 * LLM-based query classification for complex cases
 */
async function classifyQueryWithLLM(query: string): Promise<{ route: string; confidence: number; reasoning: string }> {
  try {
    const classificationPrompt = `You are a query router for an innovation platform. Classify this query into one of three categories:

1. RAG: Content/semantic questions about profiles, posts, innovation, advice, recommendations
2. TEXT2SQL: Analytics, counts, metrics, data queries, statistics, trends
3. HYBRID: Complex questions needing both content and data analysis

Query: "${query}"

Respond with ONLY a JSON object in this format:
{
  "route": "rag|text2sql|hybrid",
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation"
}`;

    const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: classificationPrompt }],
        temperature: 0.1,
        max_tokens: 150
      }),
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content.trim();
    
    // Parse JSON response
    const result = JSON.parse(content);
    
    return {
      route: result.route,
      confidence: Math.max(0.1, Math.min(1.0, result.confidence)),
      reasoning: result.reasoning || 'LLM classification'
    };

  } catch (error) {
    console.error('LLM classification failed:', error);
    // Fallback to pattern-based classification
    return classifyQueryByPatterns(query);
  }
}

/**
 * Get suggested approach based on route and context
 */
function getSuggestedApproach(route: string, query: string, userContext?: any): any {
  const approaches = {
    rag: {
      primary_method: 'semantic_search',
      context_needed: ['user_profiles', 'posts', 'platform_content'],
      description: 'Search platform content using semantic similarity'
    },
    text2sql: {
      primary_method: 'sql_generation',
      fallback_method: 'predefined_queries',
      context_needed: ['database_schema', 'table_relationships', 'user_permissions'],
      description: 'Generate SQL queries for data analysis'
    },
    hybrid: {
      primary_method: 'rag_then_sql',
      fallback_method: 'parallel_processing',
      context_needed: ['user_profiles', 'posts', 'database_schema', 'analytics_data'],
      description: 'Combine content search with data analysis'
    }
  };

  return approaches[route as keyof typeof approaches] || approaches.rag;
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }

  try {
    const body = await req.json() as QueryRouterRequest;
    const { query, user_context, force_route } = body;

    if (!query || typeof query !== 'string') {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid query: string required'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    console.log(`Routing query: "${query.substring(0, 100)}..."`);

    let classification;
    
    if (force_route) {
      // Use forced route
      classification = {
        route: force_route,
        confidence: 1.0,
        reasoning: 'Forced route specified by user'
      };
    } else {
      // Try pattern-based classification first (fast)
      classification = classifyQueryByPatterns(query);
      
      // Use LLM for low-confidence cases
      if (classification.confidence < 0.7 && DEEPSEEK_API_KEY) {
        console.log('Low confidence, using LLM classification');
        classification = await classifyQueryWithLLM(query);
      }
    }

    const suggestedApproach = getSuggestedApproach(classification.route, query, user_context);
    const processingTime = Date.now() - startTime;

    const response: QueryRouterResponse = {
      success: true,
      route: classification.route as 'rag' | 'text2sql' | 'hybrid',
      confidence: classification.confidence,
      reasoning: classification.reasoning,
      suggested_approach: suggestedApproach,
      processing_time_ms: processingTime
    };

    console.log(`Query routed to: ${classification.route} (confidence: ${classification.confidence})`);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Query router error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return new Response(
      JSON.stringify({
        success: false,
        processing_time_ms: processingTime,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
