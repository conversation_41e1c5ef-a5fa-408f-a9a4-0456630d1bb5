// Test script for AI chat system
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5OTU5NzQsImV4cCI6MjA2MDU3MTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testAIChat() {
  console.log('🤖 Testing AI Chat System...');

  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: "Hello! Can you tell me about the innovation opportunities in Zimbabwe?",
        user_context: {
          user_id: null // Test as guest user
        },
        rag_enabled: true,
        max_context_items: 5
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ AI Chat failed:', response.status, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ AI Chat Response:', {
      success: data.success,
      message: data.message?.substring(0, 200) + '...',
      rag_context_items: data.rag_context_used?.length || 0,
      processing_time: data.processing_time_ms + 'ms'
    });

  } catch (error) {
    console.error('❌ AI Chat test failed:', error);
  }
}

async function testRAGPopulation() {
  console.log('📊 Testing RAG Population...');

  try {
    // First, test with dry run
    const dryRunResponse = await fetch(`${SUPABASE_URL}/functions/v1/populate-rag-embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content_types: ['profile'],
        batch_size: 2,
        dry_run: true
      })
    });

    if (!dryRunResponse.ok) {
      const errorText = await dryRunResponse.text();
      console.error('❌ RAG Population dry run failed:', dryRunResponse.status, errorText);
      return;
    }

    const dryRunData = await dryRunResponse.json();
    console.log('✅ RAG Population Dry Run:', {
      success: dryRunData.success,
      content_processed: dryRunData.content_processed,
      processing_time: dryRunData.processing_time_ms + 'ms'
    });

  } catch (error) {
    console.error('❌ RAG Population test failed:', error);
  }
}

async function testEmbedFunction() {
  console.log('🔢 Testing Embed Function...');

  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/embed`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: "This is a test text for embedding generation",
        model: "gte-small"
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Embed function failed:', response.status, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ Embed Function Response:', {
      success: data.success,
      embedding_dimensions: data.embedding?.length || 0,
      model: data.model,
      processing_time: data.processing_time_ms + 'ms'
    });

  } catch (error) {
    console.error('❌ Embed function test failed:', error);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Phase 2 System Tests...\n');

  await testEmbedFunction();
  console.log('');

  await testRAGPopulation();
  console.log('');

  await testAIChat();
  console.log('');

  console.log('✅ All tests completed!');
}

runAllTests();