<!DOCTYPE html>
<html>
<head>
    <title>Test Logout</title>
</head>
<body>
    <h1>Test Logout Functionality</h1>
    <button onclick="testLogout()">Test Logout</button>
    <div id="result"></div>

    <script>
        async function testLogout() {
            try {
                // Navigate to the dashboard and test logout
                const response = await fetch('http://localhost:5173/dashboard');
                if (response.ok) {
                    document.getElementById('result').innerHTML = 'Dashboard accessible - user is logged in';
                    
                    // Try to trigger logout via console
                    window.open('http://localhost:5173/dashboard', '_blank');
                } else {
                    document.getElementById('result').innerHTML = 'Dashboard not accessible - user may not be logged in';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
