<template>
  <q-btn
    :label="label"
    :icon="icon"
    :color="color"
    :size="size"
    :outline="outline"
    :class="buttonClass"
    @click="handleTrigger"
    :loading="isLoading"
  >
    <q-tooltip v-if="tooltip">{{ tooltip }}</q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useGlobalServicesStore } from '../../stores/globalServices'

interface Props {
  triggerKey: string
  label: string
  icon?: string
  color?: string
  tooltip?: string
  context?: string
  size?: string
  outline?: boolean
  variant?: string
}

const props = withDefaults(defineProps<Props>(), {
  icon: 'psychology',
  color: 'primary',
  size: 'md',
  outline: false,
  variant: 'default'
})

const emit = defineEmits<{
  triggered: [triggerKey: string]
  success: [triggerKey: string]
  error: [triggerKey: string, error: Error]
}>()

const globalServices = useGlobalServicesStore()
const isLoading = ref(false)

const buttonClass = computed(() => {
  const classes = ['ai-trigger-button']
  if (props.variant === 'compact') {
    classes.push('compact-variant')
  }
  return classes.join(' ')
})

const handleTrigger = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    emit('triggered', props.triggerKey)

    // Use the global AI chat trigger service
    await globalServices.aiChatTriggerService.triggerChat(props.triggerKey, props.context)
    
    emit('success', props.triggerKey)
  } catch (error) {
    console.error('AI trigger error:', error)
    emit('error', props.triggerKey, error as Error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.ai-trigger-button {
  transition: all 0.2s ease;
}

.ai-trigger-button:hover {
  transform: translateY(-1px);
}

.compact-variant {
  font-size: 0.875rem;
}
</style>
