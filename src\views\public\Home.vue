<template>
  <q-page>
    <HeroSection @trigger-signin="showSignInDialog = true" @trigger-signup="showSignUpDialog = true" />
    <CountdownSection />
    <FeaturesSection />
    <EcosystemMapSection />
    <OurImpactSection />
    <div id="news-section" class="news-section">
      <NewsSection />
    </div>

    <div id="signup-section" class="sign-up-section q-py-md">
      <div class="container q-mx-auto q-px-md">
        <div class="row">
          <div class="col-1" />
          <div class="col-10">
            <div class="text-h3 text-weight-light q-mb-md text-center" style="color: #0D8A3E">Join Our Early Access Program</div>
            <p class="text-body1 text-center q-mb-lg" style="max-width: 800px; margin: 0 auto;">
              Be among the first to access our exclusive innovation ecosystem. Early members receive priority matchmaking with potential partners, mentors, and investors, plus special access to resources and events before our official launch. Shape the future of innovation with us!
            </p>
          </div>
          <div class="col-1" />
        </div>
        <EarlyAccessForm />
      </div>
    </div>

    <!-- Sign In Dialog -->
    <q-dialog v-model="showSignInDialog" persistent>
      <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Welcome Back</div>
          <q-space />
          <q-btn flat round dense v-close-popup>
            <q-icon name="close" />
          </q-btn>
        </q-card-section>

        <q-card-section>
          <auth-options
            mode="login"
            @email-password="handleEmailPasswordSignIn"
          />
        </q-card-section>

        <!-- Toggle to Sign Up -->
        <q-card-section class="text-center q-pt-none">
          <q-btn
            flat
            no-caps
            color="primary"
            @click="switchToSignUp"
            class="text-caption"
          >
            Don't have an account? Sign Up
          </q-btn>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Sign Up Dialog -->
    <q-dialog v-model="showSignUpDialog" persistent>
      <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Join Our Early Access Program</div>
          <q-space />
          <q-btn flat round dense v-close-popup>
            <q-icon name="close" />
          </q-btn>
        </q-card-section>

        <q-card-section>
          <auth-options
            mode="signup"
            @email-password-signup="handleEmailPasswordSignUp"
          />
        </q-card-section>

        <!-- Toggle to Sign In -->
        <q-card-section class="text-center q-pt-none">
          <q-btn
            flat
            no-caps
            color="primary"
            @click="switchToSignIn"
            class="text-caption"
          >
            Already have an account? Sign In
          </q-btn>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Email/Password Sign In Form Dialog -->
    <q-dialog v-model="showEmailPasswordSignInForm" persistent>
      <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Sign In</div>
          <q-space />
          <q-btn flat round dense v-close-popup>
            <q-icon name="close" />
          </q-btn>
        </q-card-section>

        <q-card-section>
          <q-form @submit="handleSignIn" class="q-gutter-md">
            <q-input
              v-model="signInForm.email"
              type="email"
              label="Email"
              :rules="emailRules"
              outlined
              dense
            />
            <q-input
              v-model="signInForm.password"
              :type="isPwd ? 'password' : 'text'"
              label="Password"
              :rules="passwordRules"
              outlined
              dense
            >
              <template v-slot:append>
                <q-icon
                  :name="isPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isPwd = !isPwd"
                />
              </template>
            </q-input>
            <q-btn
              type="submit"
              color="primary"
              label="Sign In"
              :loading="loading"
              class="full-width"
              no-caps
            />
          </q-form>
        </q-card-section>

        <!-- Toggle to Sign Up -->
        <q-card-section class="text-center q-pt-none">
          <q-btn
            flat
            no-caps
            color="primary"
            @click="switchToSignUpForm"
            class="text-caption"
          >
            Don't have an account? Sign Up
          </q-btn>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Email/Password Sign Up Form Dialog -->
    <q-dialog v-model="showEmailPasswordSignUpForm" persistent>
      <q-card class="auth-dialog" style="min-width: 350px; max-width: 400px">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Create Account</div>
          <q-space />
          <q-btn flat round dense v-close-popup>
            <q-icon name="close" />
          </q-btn>
        </q-card-section>

        <q-card-section>
          <q-form @submit="handleSignUp" class="q-gutter-md">
            <q-input
              v-model="signUpForm.email"
              type="email"
              label="Email"
              :rules="emailRules"
              outlined
              dense
            />
            <q-input
              v-model="signUpForm.password"
              :type="isPwd ? 'password' : 'text'"
              label="Password"
              :rules="passwordRules"
              outlined
              dense
            >
              <template v-slot:append>
                <q-icon
                  :name="isPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isPwd = !isPwd"
                />
              </template>
            </q-input>
            <q-btn
              type="submit"
              color="primary"
              label="Create Account"
              :loading="loading"
              class="full-width"
              no-caps
            />
          </q-form>
        </q-card-section>

        <!-- Toggle to Sign In -->
        <q-card-section class="text-center q-pt-none">
          <q-btn
            flat
            no-caps
            color="primary"
            @click="switchToSignInForm"
            class="text-caption"
          >
            Already have an account? Sign In
          </q-btn>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { defineAsyncComponent, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useNotificationStore } from '@/stores/notifications'
import AuthOptions from '../../components/auth/AuthOptions.vue'

const HeroSection = defineAsyncComponent(() => import('../../components/public/HeroSection.vue'))
const CountdownSection = defineAsyncComponent(() => import('../../components/public/CountdownSection.vue'))
const NewsSection = defineAsyncComponent(() => import('../../components/public/NewsSection.vue'))
const EarlyAccessForm = defineAsyncComponent(() => import('../../components/public/EarlyAccessForm.vue'))
const EcosystemMapSection = defineAsyncComponent(() => import('../../components/public/EcosystemMapSection.vue'))
const OurImpactSection = defineAsyncComponent(() => import('../../components/public/OurImpactSection.vue'))
const FeaturesSection = defineAsyncComponent(() => import('../../components/public/FeaturesSection.vue'))

const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

// Dialog controls
const showSignInDialog = ref(false)
const showSignUpDialog = ref(false)
const showEmailPasswordSignInForm = ref(false)
const showEmailPasswordSignUpForm = ref(false)
const loading = ref(false)
const isPwd = ref(true)

// Form data
const signInForm = ref({
  email: '',
  password: ''
})

const signUpForm = ref({
  email: '',
  password: ''
})

// Form validation rules
const emailRules = [
  (val: string) => !!val || 'Email is required',
  (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
]

const passwordRules = [
  (val: string) => !!val || 'Password is required',
  (val: string) => val.length >= 6 || 'Password must be at least 6 characters'
]

// Dialog switching functions
const switchToSignUp = () => {
  showSignInDialog.value = false
  showSignUpDialog.value = true
}

const switchToSignIn = () => {
  showSignUpDialog.value = false
  showSignInDialog.value = true
}

// Handle email password sign in
const handleEmailPasswordSignIn = () => {
  showSignInDialog.value = false
  showEmailPasswordSignInForm.value = true
}

// Handle email password sign up
const handleEmailPasswordSignUp = () => {
  showSignUpDialog.value = false
  showEmailPasswordSignUpForm.value = true
}

// Form dialog switching functions
const switchToSignUpForm = () => {
  showEmailPasswordSignInForm.value = false
  showEmailPasswordSignUpForm.value = true
}

const switchToSignInForm = () => {
  showEmailPasswordSignUpForm.value = false
  showEmailPasswordSignInForm.value = true
}

// Handle sign in form submission
const handleSignIn = async () => {
  loading.value = true
  try {
    await authStore.signIn(signInForm.value.email, signInForm.value.password)
    showEmailPasswordSignInForm.value = false
    notificationStore.success('Successfully signed in!')
    router.push('/dashboard')
  } catch (error: any) {
    console.error('Sign in error:', error)
    notificationStore.error(error.message || 'Failed to sign in. Please try again.')
  } finally {
    loading.value = false
  }
}

// Handle sign up form submission
const handleSignUp = async () => {
  loading.value = true
  try {
    await authStore.signUp(signUpForm.value.email, signUpForm.value.password)
    showEmailPasswordSignUpForm.value = false
    notificationStore.success('Account created successfully! Please check your email for verification.')
  } catch (error: any) {
    console.error('Sign up error:', error)
    notificationStore.error(error.message || 'Failed to create account. Please try again.')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.sign-up-section {
  background-color: #f5f5f5;
  scroll-margin-top: 60px; /* Reduced space for the fixed header */
  scroll-behavior: smooth;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.news-section {
  scroll-margin-top: 60px; /* Reduced space for the fixed header */
  scroll-behavior: smooth;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

/* Rubik font for the entire app */
body {
  font-family: 'Rubik', sans-serif;
  scroll-behavior: smooth;
}

@media (max-width: 599px) {
  .container {
    padding: 0 16px !important;
  }

  .col-1 {
    display: none;
  }

  .col-10 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .sign-up-section {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }

  .news-section {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }

  .q-py-xl {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }
}
</style>