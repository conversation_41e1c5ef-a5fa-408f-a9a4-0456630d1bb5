/**
 * Sample Data Service
 * 
 * Service to populate the database with sample success stories and news articles
 */

import { usePostsStore } from '../stores/posts';
import { sampleSuccessStories, sampleNewsArticles } from '../data/sampleData';

export class SampleDataService {
  private postsStore = usePostsStore();

  /**
   * Add sample success stories to the database
   */
  async addSampleSuccessStories(): Promise<void> {
    console.log('Adding sample success stories...');
    
    for (const story of sampleSuccessStories) {
      try {
        const postData = {
          title: story.title,
          content: story.content,
          excerpt: story.excerpt,
          featuredImage: story.featuredImage,
          postType: 'SUCCESS_STORY',
          subType: story.subType,
          status: 'published',
          tags: story.tags,
          slug: story.slug,
          // Success story specific fields
          achievementType: story.achievementType,
          achievementDetails: story.achievementDetails,
          // Author information (will be handled by the system)
          author: story.author,
          authorRole: story.authorRole,
          authorAvatar: story.authorAvatar,
          // Additional metadata
          successStats: JSON.stringify(story.stats),
          category: 'Success Stories',
          blogCategory: 'Success Stories'
        };

        const result = await this.postsStore.createPost(postData);
        if (result) {
          console.log(`✅ Added success story: ${story.title}`);
        } else {
          console.error(`❌ Failed to add success story: ${story.title}`);
        }
      } catch (error) {
        console.error(`❌ Error adding success story "${story.title}":`, error);
      }
    }
  }

  /**
   * Add sample news articles to the database
   */
  async addSampleNewsArticles(): Promise<void> {
    console.log('Adding sample news articles...');
    
    for (const article of sampleNewsArticles) {
      try {
        const postData = {
          title: article.title,
          content: article.content,
          excerpt: article.excerpt,
          featuredImage: article.featuredImage,
          postType: 'BLOG_ARTICLE',
          subType: 'news',
          status: 'published',
          tags: article.tags,
          slug: article.slug,
          // Blog specific fields
          category: article.category,
          blogCategory: article.category,
          // Featured status
          isFeatured: article.isFeatured || false,
          // Additional metadata for news
          newsCategory: article.category,
          categoryColor: article.categoryColor,
          textColor: article.textColor
        };

        const result = await this.postsStore.createPost(postData);
        if (result) {
          console.log(`✅ Added news article: ${article.title}`);
        } else {
          console.error(`❌ Failed to add news article: ${article.title}`);
        }
      } catch (error) {
        console.error(`❌ Error adding news article "${article.title}":`, error);
      }
    }
  }

  /**
   * Add all sample data
   */
  async addAllSampleData(): Promise<void> {
    console.log('🚀 Starting to add all sample data...');
    
    try {
      await this.addSampleSuccessStories();
      await this.addSampleNewsArticles();
      console.log('✅ All sample data added successfully!');
    } catch (error) {
      console.error('❌ Error adding sample data:', error);
    }
  }

  /**
   * Check if sample data already exists
   */
  async checkSampleDataExists(): Promise<boolean> {
    try {
      // Check if any of our sample stories exist by slug
      const sampleSlugs = [
        ...sampleSuccessStories.map(s => s.slug),
        ...sampleNewsArticles.map(a => a.slug)
      ];

      // Fetch posts to check if sample data exists
      await this.postsStore.fetchPosts();
      const existingPosts = this.postsStore.posts;
      
      const existingSlugs = existingPosts
        .filter(post => post.slug)
        .map(post => post.slug);

      const hasExistingData = sampleSlugs.some(slug => existingSlugs.includes(slug));
      
      if (hasExistingData) {
        console.log('📋 Sample data already exists in the database');
      } else {
        console.log('📋 No sample data found in the database');
      }
      
      return hasExistingData;
    } catch (error) {
      console.error('❌ Error checking sample data:', error);
      return false;
    }
  }

  /**
   * Initialize sample data if it doesn't exist
   */
  async initializeSampleData(): Promise<void> {
    const exists = await this.checkSampleDataExists();
    
    if (!exists) {
      console.log('🔄 Initializing sample data...');
      await this.addAllSampleData();
    } else {
      console.log('✅ Sample data already exists, skipping initialization');
    }
  }
}

// Export a singleton instance
export const sampleDataService = new SampleDataService();
