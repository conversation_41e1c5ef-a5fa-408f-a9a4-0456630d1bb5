<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #0D8A3E;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0a7235;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .response {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .loading {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI Chat Direct Test</h1>
        <p>This page tests the AI chat functionality directly by calling the Supabase Edge Function.</p>
        
        <div class="test-section">
            <h3>Test 1: Basic AI Chat</h3>
            <button onclick="testBasicChat()" id="basicTestBtn">Test Basic Chat</button>
            <div id="basicResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: AI Chat with Context</h3>
            <button onclick="testChatWithContext()" id="contextTestBtn">Test Chat with Context</button>
            <div id="contextResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Error Handling</h3>
            <button onclick="testErrorHandling()" id="errorTestBtn">Test Error Handling</button>
            <div id="errorResult" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

        async function callAIChat(requestBody, resultElementId, buttonId) {
            const resultElement = document.getElementById(resultElementId);
            const button = document.getElementById(buttonId);
            
            resultElement.style.display = 'block';
            resultElement.className = 'response loading';
            resultElement.textContent = '🔄 Testing AI chat...';
            button.disabled = true;

            try {
                console.log('📤 Sending request:', requestBody);

                const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'apikey': SUPABASE_ANON_KEY,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('📥 Response status:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log('✅ Response data:', data);

                if (data.success && data.message) {
                    resultElement.className = 'response success';
                    resultElement.textContent = `✅ SUCCESS!\n\nAI Response:\n${data.message}\n\nProcessing time: ${data.processing_time_ms}ms`;
                } else {
                    resultElement.className = 'response error';
                    resultElement.textContent = `⚠️ Response received but no AI message:\n${JSON.stringify(data, null, 2)}`;
                }

            } catch (error) {
                console.error('❌ Error:', error);
                resultElement.className = 'response error';
                resultElement.textContent = `❌ ERROR:\n${error.message}`;
            } finally {
                button.disabled = false;
            }
        }

        async function testBasicChat() {
            const requestBody = {
                message: "Hello, can you tell me about the ZbInnovation platform?",
                conversation_history: [],
                user_context: {
                    is_authenticated: true,
                    user_id: "test-user-123",
                    profile_type: "innovator",
                    current_page: "home"
                }
            };

            await callAIChat(requestBody, 'basicResult', 'basicTestBtn');
        }

        async function testChatWithContext() {
            const requestBody = {
                message: "What features does the platform offer for entrepreneurs?",
                conversation_history: [
                    { role: 'user', content: 'Hello, can you tell me about the ZbInnovation platform?' },
                    { role: 'assistant', content: 'ZbInnovation is an innovation ecosystem platform that connects innovators, investors, mentors, and other stakeholders.' }
                ],
                user_context: {
                    is_authenticated: true,
                    user_id: "test-user-123",
                    profile_type: "innovator",
                    current_page: "home"
                },
                rag_enabled: true,
                max_context_items: 8
            };

            await callAIChat(requestBody, 'contextResult', 'contextTestBtn');
        }

        async function testErrorHandling() {
            const requestBody = {
                message: "", // Empty message to test error handling
                user_context: {
                    is_authenticated: false
                }
            };

            await callAIChat(requestBody, 'errorResult', 'errorTestBtn');
        }
    </script>
</body>
</html>
