import { test, expect } from '@playwright/test';

test.describe('AI Implementation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5173');
  });

  test('AI Chat Assistant is available on landing page', async ({ page }) => {
    // Check if AI chat toggle button is present
    const aiChatToggle = page.getByTestId('ai-chat-toggle');
    await expect(aiChatToggle).toBeVisible();
    
    // Check tooltip
    await aiChatToggle.hover();
    await expect(page.locator('.q-tooltip')).toContainText('AI Assistant');
  });

  test('AI Chat opens and displays welcome message', async ({ page }) => {
    // Click AI chat toggle
    await page.getByTestId('ai-chat-toggle').click();
    
    // Wait for chat window to open
    await expect(page.getByTestId('ai-chat-container')).toBeVisible();
    
    // Check for welcome message
    await expect(page.locator('.welcome-message')).toBeVisible();
    await expect(page.locator('.welcome-message')).toContainText('ZbInnovation AI Assistant');
  });

  test('AI Chat input and send functionality', async ({ page }) => {
    // Open AI chat
    await page.getByTestId('ai-chat-toggle').click();
    
    // Type a message
    const chatInput = page.getByTestId('ai-chat-input');
    await chatInput.fill('Hello, can you help me?');
    
    // Send message
    await page.getByTestId('ai-chat-send').click();
    
    // Check if user message appears
    await expect(page.getByTestId('user-message')).toContainText('Hello, can you help me?');
    
    // Wait for AI response (with timeout)
    await expect(page.getByTestId('ai-message')).toBeVisible({ timeout: 10000 });
  });

  test('AI Chat suggestions are clickable', async ({ page }) => {
    // Open AI chat
    await page.getByTestId('ai-chat-toggle').click();
    
    // Wait for welcome suggestions
    await expect(page.locator('.suggestion-chip')).toBeVisible();
    
    // Click first suggestion
    const firstSuggestion = page.locator('.suggestion-chip').first();
    const suggestionText = await firstSuggestion.textContent();
    await firstSuggestion.click();
    
    // Check if suggestion was sent as message
    await expect(page.getByTestId('user-message')).toContainText(suggestionText || '');
  });

  test('AI Chat action buttons work correctly', async ({ page }) => {
    // Open AI chat and send a message that should return action buttons
    await page.getByTestId('ai-chat-toggle').click();
    
    const chatInput = page.getByTestId('ai-chat-input');
    await chatInput.fill('Help me get started on the platform');
    await page.getByTestId('ai-chat-send').click();
    
    // Wait for AI response with actions
    await expect(page.getByTestId('ai-message')).toBeVisible({ timeout: 10000 });
    
    // Check if action buttons are present
    const actionButtons = page.locator('.ai-action-button');
    if (await actionButtons.count() > 0) {
      // Click first action button
      await actionButtons.first().click();
      
      // Verify action was executed (could be navigation or dialog)
      // This will depend on the specific action
    }
  });

  test('AI Trigger buttons work on dashboard', async ({ page }) => {
    // Login first (using test credentials)
    await page.goto('http://localhost:5173/auth/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Superb@23');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/dashboard');
    
    // Check for AI trigger buttons
    const aiTriggers = page.locator('[data-testid^="ai-trigger-"]');
    await expect(aiTriggers.first()).toBeVisible();
    
    // Click an AI trigger
    await aiTriggers.first().click();
    
    // Verify AI chat opens
    await expect(page.getByTestId('ai-chat-container')).toBeVisible();
    
    // Verify message was sent
    await expect(page.getByTestId('user-message')).toBeVisible();
  });

  test('AI Trigger buttons work on community page', async ({ page }) => {
    // Navigate to community page
    await page.goto('http://localhost:5173/virtual-community');
    
    // Check for AI trigger buttons in filter section
    const aiTriggers = page.locator('[data-testid^="ai-trigger-"]');
    if (await aiTriggers.count() > 0) {
      // Click an AI trigger
      await aiTriggers.first().click();
      
      // Verify AI chat opens
      await expect(page.getByTestId('ai-chat-container')).toBeVisible();
    }
  });

  test('AI Chat maintains context between messages', async ({ page }) => {
    // Open AI chat
    await page.getByTestId('ai-chat-toggle').click();
    
    // Send first message
    await page.getByTestId('ai-chat-input').fill('I am an innovator');
    await page.getByTestId('ai-chat-send').click();
    
    // Wait for response
    await expect(page.getByTestId('ai-message')).toBeVisible({ timeout: 10000 });
    
    // Send follow-up message
    await page.getByTestId('ai-chat-input').fill('What should I do next?');
    await page.getByTestId('ai-chat-send').click();
    
    // Wait for contextual response
    await expect(page.getByTestId('ai-message').nth(1)).toBeVisible({ timeout: 10000 });
    
    // Verify conversation history is maintained
    const messages = page.getByTestId('user-message');
    await expect(messages).toHaveCount(2);
  });

  test('AI Chat streaming works correctly', async ({ page }) => {
    // Open AI chat
    await page.getByTestId('ai-chat-toggle').click();
    
    // Send a message that should trigger streaming
    await page.getByTestId('ai-chat-input').fill('Tell me about innovation opportunities in Zimbabwe');
    await page.getByTestId('ai-chat-send').click();
    
    // Wait for AI response to start
    await expect(page.getByTestId('ai-message')).toBeVisible({ timeout: 10000 });
    
    // Check if content is being streamed (content should grow over time)
    const aiMessage = page.getByTestId('ai-message').last();
    const initialContent = await aiMessage.textContent();
    
    // Wait a bit and check if content has grown
    await page.waitForTimeout(2000);
    const finalContent = await aiMessage.textContent();
    
    // Content should be different (streaming effect)
    expect(finalContent?.length).toBeGreaterThanOrEqual(initialContent?.length || 0);
  });

  test('AI Chat error handling works correctly', async ({ page }) => {
    // Mock network failure or API error
    await page.route('**/functions/v1/ai-enhanced-chat', route => {
      route.abort('failed');
    });
    
    // Open AI chat and send message
    await page.getByTestId('ai-chat-toggle').click();
    await page.getByTestId('ai-chat-input').fill('Test error handling');
    await page.getByTestId('ai-chat-send').click();
    
    // Should show error message
    await expect(page.getByTestId('ai-message')).toContainText('system upgrade');
  });

  test('AI Chat closes correctly', async ({ page }) => {
    // Open AI chat
    await page.getByTestId('ai-chat-toggle').click();
    await expect(page.getByTestId('ai-chat-container')).toBeVisible();
    
    // Close AI chat
    await page.locator('.chat-header q-btn[icon="close"]').click();
    
    // Verify chat is closed
    await expect(page.getByTestId('ai-chat-container')).not.toBeVisible();
    
    // Verify toggle button is visible again
    await expect(page.getByTestId('ai-chat-toggle')).toBeVisible();
  });
});

test.describe('AI Edge Function Tests', () => {
  test('AI Edge Function responds correctly', async ({ request }) => {
    // Test the AI Edge Function directly
    const response = await request.post('http://localhost:54321/functions/v1/ai-enhanced-chat', {
      data: {
        message: 'Hello, test message',
        user_context: {
          is_authenticated: false,
          current_page: 'landing'
        }
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('response');
    expect(data).toHaveProperty('conversation_id');
    expect(data.response).toBeTruthy();
  });
});
