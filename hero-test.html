<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Section Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Rubik', sans-serif;
        }
        
        .hero-section {
            min-height: 600px;
            background-image: url('https://ext.same-assets.com/4258758033/1505177164.png');
            background-size: cover;
            background-position: center;
            color: white;
            position: relative;
            overflow: hidden;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            width: 100%;
            max-width: 1400px;
            z-index: 2;
            position: relative;
            padding: 0 20px;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 600px;
            padding-top: 64px;
        }
        
        .centered-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            margin-top: 80px;
            text-align: center;
        }

        .logo-container {
            position: relative;
            z-index: 3;
            margin-bottom: -35px;
        }
        
        .logo-bg {
            background-color: white;
            border-radius: 50%;
            width: 70px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px;
            margin: 0 auto;
        }
        
        .mini-logo {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .translucent-section {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            width: 100%;
            padding: 40px 40px 30px;
            color: white;
            text-align: center;
        }

        .innovation-text {
            font-weight: 700;
            font-size: 3rem;
            line-height: 1;
            color: #a4ca39;
            text-align: center;
            margin-bottom: 10px;
        }

        .action-buttons-inside {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            min-width: 160px;
            height: 40px;
            text-align: center;
            transition: all 0.3s ease;
            box-sizing: border-box;
            line-height: 16px;
        }
        
        .btn-outline {
            background: transparent;
            color: #a4ca39;
            border: 2px solid #a4ca39;
        }
        
        .btn-outline:hover {
            background: #a4ca39;
            color: white;
        }
        
        .btn-primary {
            background: #0D8A3E;
            color: white;
            border: 2px solid #0D8A3E;
        }
        
        .btn-primary:hover {
            background: #0a6b31;
            border-color: #0a6b31;
        }
        
        @media (max-width: 767px) {
            .hero-section {
                padding-top: 40px;
                padding-bottom: 0;
            }
            
            .innovation-text {
                font-size: 2.2rem;
            }
            
            .translucent-section {
                margin-top: 20px;
                padding: 40px 20px 30px;
            }
            
            .logo-container {
                margin-bottom: -25px;
            }
            
            .action-buttons-inside {
                margin-top: 15px;
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
        }
        
        @media (max-width: 599px) {
            .hero-section {
                min-height: auto;
                padding-top: 40px;
                margin-top: -40px;
                padding-bottom: 40px;
                background-position: 70% center;
            }
            
            .hero-content {
                padding-top: 30px !important;
                padding-bottom: 20px !important;
            }
            
            .translucent-section {
                margin: 20px 0 !important;
                padding: 40px 15px 25px !important;
                backdrop-filter: blur(10px);
                background-color: rgba(255, 255, 255, 0.2);
            }
            
            .innovation-text {
                font-size: 2rem !important;
                margin-bottom: 15px !important;
                line-height: 0.9;
            }
            
            .logo-bg {
                width: 50px;
                height: 50px;
            }
            
            .logo-container {
                margin-bottom: -20px !important;
            }
            
            .action-buttons {
                margin-top: 15px !important;
            }
            
            .btn {
                margin: 8px 16px !important;
                width: calc(100% - 32px) !important;
            }
        }
    </style>
</head>
<body>
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <!-- Centered content with logo positioned above translucent section -->
                <div class="centered-content">
                    <!-- Logo positioned 50% above the translucent section -->
                    <div class="logo-container">
                        <div class="logo-bg">
                            <img src="/logo.png" alt="ZbInnovation Logo" class="mini-logo" style="height: 80px;" width="80" height="80">
                        </div>
                    </div>
                    
                    <!-- Translucent background section occupying full column -->
                    <div class="translucent-section">
                        <h1 class="innovation-text">
                            Innovation Community
                        </h1>
                        <p style="font-size: 1.1rem; margin-bottom: 10px; font-weight: 300;">
                            Be part of the innovation revolution; connect with like-minded players in the innovation ecosystem.
                        </p>
                        <p style="font-size: 1.3rem; margin-bottom: 10px; font-weight: bold;">
                            <strong>Innovators, Investors, Mentors, Industry Experts, Academic Student, Academic Institutions, Government</strong>
                        </p>
                        <p style="font-size: 1rem; margin-bottom: 15px; font-weight: 300;">
                            Join us, and be part of an amazing community
                        </p>

                        <!-- Action buttons inside the translucent section -->
                        <div class="action-buttons-inside">
                            <a href="#" class="btn btn-outline" style="color: #6c757d; border-color: #6c757d;" onclick="alert('AI Learn More trigger would be activated here')">Learn More</a>
                            <a href="#signup-section" class="btn btn-primary">Sign In</a>
                            <a href="/virtual-community" class="btn btn-outline" style="color: #a4ca39; border-color: #a4ca39;">Explore Community</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
