import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Types for AI chat
interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: string;
}

interface ChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context?: {
    user_id?: string;
    profile_status?: 'not_started' | 'in_progress' | 'completed';
    profile_type?: string;
  };
  rag_enabled?: boolean;
  max_context_items?: number;
}

interface ChatResponse {
  success: boolean;
  message?: string;
  conversation_id?: string;
  rag_context_used?: any[];
  query_route?: 'rag' | 'text2sql' | 'hybrid';
  route_confidence?: number;
  processing_time_ms: number;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Claude API configuration
const CLAUDE_API_KEY = Deno.env.get('CLAUDE_API') || Deno.env.get('CLAUDE_API_KEY');
const CLAUDE_BASE_URL = 'https://api.anthropic.com';

/**
 * Route query to determine processing approach
 */
async function routeQuery(query: string, userContext?: any): Promise<{
  route: 'rag' | 'text2sql' | 'hybrid';
  confidence: number;
  reasoning: string;
}> {
  try {
    const { data, error } = await supabase.functions.invoke('query-router', {
      body: {
        query,
        user_context: userContext
      }
    });

    if (error) {
      console.error('Query routing error:', error);
      // Fallback to RAG for safety
      return {
        route: 'rag',
        confidence: 0.5,
        reasoning: 'Fallback to RAG due to routing error'
      };
    }

    return {
      route: data.route,
      confidence: data.confidence,
      reasoning: data.reasoning
    };
  } catch (error) {
    console.error('Error routing query:', error);
    return {
      route: 'rag',
      confidence: 0.5,
      reasoning: 'Fallback to RAG due to routing failure'
    };
  }
}

/**
 * Generate production-grade embedding for user query
 */
async function generateQueryEmbedding(text: string): Promise<number[]> {
  try {
    const { data, error } = await supabase.functions.invoke('embed', {
      body: {
        input: text,
        model: 'bge-m3', // Use production model
        chunk_strategy: 'none' // Don't chunk queries
      }
    });

    if (error) {
      console.error('Query embedding error:', error);
      throw new Error(`Failed to generate query embedding: ${error.message}`);
    }

    return data.embedding;
  } catch (error) {
    console.error('Error generating query embedding:', error);
    throw error;
  }
}

/**
 * Enhanced RAG context retrieval with contextual filtering
 */
async function getEnhancedRAGContext(
  userMessage: string,
  route: 'rag' | 'text2sql' | 'hybrid',
  userId?: string,
  maxItems: number = 10
): Promise<any[]> {
  try {
    // Generate embedding for user query using production model
    const queryEmbedding = await generateQueryEmbedding(userMessage);

    // Adjust context retrieval based on query route
    let contextTypes: string[] | null = null;
    let similarityThreshold = 0.6;

    if (route === 'rag') {
      // For RAG queries, focus on content and profiles
      contextTypes = ['profile', 'post'];
      similarityThreshold = 0.65; // Higher threshold for content queries
    } else if (route === 'text2sql') {
      // For analytics queries, we might want different context
      contextTypes = ['profile']; // Focus on profile metadata
      similarityThreshold = 0.5; // Lower threshold for broader context
    } else {
      // Hybrid - get diverse context
      contextTypes = null; // All types
      similarityThreshold = 0.6;
    }

    // Get relevant context using enhanced RAG function
    const { data: context, error } = await supabase.rpc('get_rag_context', {
      query_embedding: `[${queryEmbedding.join(',')}]`,
      user_id_param: userId || null,
      context_types: contextTypes,
      max_context_items: maxItems,
      similarity_threshold: similarityThreshold
    });

    if (error) {
      console.error('Enhanced RAG context error:', error);
      return [];
    }

    // Add contextual enrichment
    const enrichedContext = (context || []).map((item: any) => ({
      ...item,
      context_source: 'rag_retrieval',
      query_route: route,
      relevance_score: item.relevance_score || 0
    }));

    console.log(`Retrieved ${enrichedContext.length} contextual items for ${route} query`);
    return enrichedContext;
  } catch (error) {
    console.error('Error getting enhanced RAG context:', error);
    return [];
  }
}

/**
 * Get analytics context for text2sql queries
 */
async function getAnalyticsContext(userMessage: string): Promise<any[]> {
  try {
    // Get platform statistics and analytics data
    const { data: platformStats } = await supabase.rpc('get_platform_stats_for_ai');

    // Extract relevant analytics based on query
    const analyticsContext = [];

    if (userMessage.toLowerCase().includes('count') || userMessage.toLowerCase().includes('how many')) {
      analyticsContext.push({
        content_snippet: `Platform Statistics: ${JSON.stringify(platformStats)}`,
        content_type: 'analytics',
        relevance_score: 0.9,
        context_source: 'analytics_data'
      });
    }

    return analyticsContext;
  } catch (error) {
    console.error('Error getting analytics context:', error);
    return [];
  }
}

/**
 * Get user profile status for context
 */
async function getUserProfileStatus(userId: string): Promise<{
  profile_status: string;
  profile_type?: string;
  profile_name?: string;
}> {
  try {
    // Check different profile types
    const profileChecks = [
      { table: 'innovator_profiles', type: 'innovator' },
      { table: 'investor_profiles', type: 'investor' },
      { table: 'mentor_profiles', type: 'mentor' },
      { table: 'professional_profiles', type: 'professional' }
    ];

    for (const check of profileChecks) {
      const { data, error } = await supabase
        .from(check.table)
        .select('profile_name, completion_percentage')
        .eq('user_id', userId)
        .single();

      if (!error && data) {
        const completionPercentage = data.completion_percentage || 0;
        return {
          profile_status: completionPercentage >= 80 ? 'completed' : 
                         completionPercentage >= 20 ? 'in_progress' : 'not_started',
          profile_type: check.type,
          profile_name: data.profile_name
        };
      }
    }

    return { profile_status: 'not_started' };
  } catch (error) {
    console.error('Error getting user profile status:', error);
    return { profile_status: 'unknown' };
  }
}

/**
 * Build enhanced system prompt with route-aware context
 */
function buildEnhancedSystemPrompt(
  userContext: any,
  ragContext: any[],
  platformStats: any,
  queryRoute: string,
  queryReasoning: string
): string {
  const authStatus = userContext.user_id ? 'authenticated' : 'guest';
  const profileStatus = userContext.profile_status || 'unknown';
  const profileType = userContext.profile_type || 'unknown';

  let systemPrompt = `You are an intelligent AI assistant for ZbInnovation - Zimbabwe's premier innovation ecosystem platform.

Query Analysis: ${queryReasoning}
Processing Mode: ${queryRoute.toUpperCase()}

User Context:
- Authentication: ${authStatus}
- Profile Status: ${profileStatus}
- Profile Type: ${profileType}`;

  if (userContext.profile_name) {
    systemPrompt += `\n- Profile Name: ${userContext.profile_name}`;
  }

  // Add platform context
  if (platformStats) {
    systemPrompt += `\n\nPlatform Overview:
- Total Profiles: ${platformStats.total_profiles || 'N/A'}
- Total Posts: ${platformStats.total_posts || 'N/A'}
- Active Community: Innovators, Investors, Mentors, Professionals, Students, Institutions`;
  }

  // Add route-specific context
  if (ragContext && ragContext.length > 0) {
    systemPrompt += `\n\nRelevant Platform Content (${ragContext.length} items):`;
    ragContext.forEach((item, index) => {
      const relevanceScore = item.relevance_score ? ` (${(item.relevance_score * 100).toFixed(0)}% match)` : '';
      systemPrompt += `\n${index + 1}. [${item.content_type}] ${item.content_snippet}${relevanceScore}`;
    });
  }

  // Route-specific instructions
  if (queryRoute === 'rag') {
    systemPrompt += `\n\nRAG Mode Instructions:
- Focus on semantic understanding and content-based responses
- Reference specific profiles, posts, and platform content when relevant
- Provide personalized recommendations based on user context
- Help with connections, collaborations, and opportunities`;
  } else if (queryRoute === 'text2sql') {
    systemPrompt += `\n\nAnalytics Mode Instructions:
- Focus on data analysis and quantitative insights
- Provide statistics, trends, and numerical information
- Reference platform metrics and growth data
- Help with data-driven decision making`;
  } else {
    systemPrompt += `\n\nHybrid Mode Instructions:
- Combine content insights with analytical data
- Provide comprehensive responses with both qualitative and quantitative information
- Reference both specific content and platform statistics
- Offer strategic recommendations based on data and content`;
  }

  systemPrompt += `\n\nGeneral Instructions:
- Be helpful, accurate, and encouraging about innovation and entrepreneurship
- Suggest appropriate actions based on user's profile status and query type
- If you don't have specific information, say so rather than guessing
- Keep responses informative but concise
- Always maintain a supportive tone for the innovation community`;

  return systemPrompt;
}

/**
 * Call Claude API for chat completion
 */
async function callClaudeAPI(
  messages: ChatMessage[],
  stream: boolean = false
): Promise<string> {
  try {
    if (!CLAUDE_API_KEY) {
      throw new Error('Claude API key not found in environment variables');
    }

    console.log('Calling Claude API with key:', CLAUDE_API_KEY ? 'Present' : 'Missing');

    // Convert messages to Claude format
    const systemMessage = messages.find(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');

    const requestBody = {
      model: 'claude-3-haiku-20240307', // Fast and cost-effective model
      max_tokens: 1000,
      temperature: 0.7,
      system: systemMessage?.content || 'You are a helpful AI assistant for ZbInnovation platform.',
      messages: conversationMessages.map(msg => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content
      }))
    };

    console.log('Claude API request:', {
      model: requestBody.model,
      messageCount: requestBody.messages.length,
      systemPromptLength: requestBody.system.length
    });

    const response = await fetch(`${CLAUDE_BASE_URL}/v1/messages`, {
      method: 'POST',
      headers: {
        'x-api-key': CLAUDE_API_KEY,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody),
    });

    console.log('Claude API response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Claude API error response:', errorText);
      throw new Error(`Claude API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Claude API response structure:', {
      hasContent: !!data.content,
      contentLength: data.content?.length || 0,
      stopReason: data.stop_reason,
      usage: data.usage
    });

    if (!data.content || !data.content[0] || !data.content[0].text) {
      throw new Error(`Invalid response format from Claude API: ${JSON.stringify(data)}`);
    }

    return data.content[0].text;
  } catch (error) {
    console.error('Claude API error:', error);
    throw error;
  }
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }

  try {
    const body = await req.json() as ChatRequest;
    const { 
      message, 
      conversation_history = [], 
      user_context = {},
      rag_enabled = true,
      max_context_items = 8
    } = body;

    console.log(`Processing AI chat request: "${message.substring(0, 50)}..."`);

    // Step 1: Route the query to determine processing approach
    const queryRouting = await routeQuery(message, user_context);
    console.log(`Query routed to: ${queryRouting.route} (confidence: ${queryRouting.confidence})`);

    // Step 2: Get user profile status if user_id provided
    let enhancedUserContext = { ...user_context };
    if (user_context.user_id) {
      const profileStatus = await getUserProfileStatus(user_context.user_id);
      enhancedUserContext = { ...enhancedUserContext, ...profileStatus };
    }

    // Step 3: Get context based on query route
    let allContext: any[] = [];
    if (rag_enabled) {
      // Get RAG context based on route
      const ragContext = await getEnhancedRAGContext(
        message,
        queryRouting.route,
        user_context.user_id,
        max_context_items
      );
      allContext.push(...ragContext);

      // For text2sql or hybrid queries, also get analytics context
      if (queryRouting.route === 'text2sql' || queryRouting.route === 'hybrid') {
        const analyticsContext = await getAnalyticsContext(message);
        allContext.push(...analyticsContext);
      }

      console.log(`Retrieved ${allContext.length} total context items for ${queryRouting.route} query`);
    }

    // Step 4: Get platform stats for context
    const { data: platformStats } = await supabase.rpc('get_platform_stats_for_ai');

    // Step 5: Build enhanced system prompt with route awareness
    const systemPrompt = buildEnhancedSystemPrompt(
      enhancedUserContext,
      allContext,
      platformStats,
      queryRouting.route,
      queryRouting.reasoning
    );

    // Prepare messages for Claude
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...conversation_history.slice(-6), // Keep last 6 messages for context
      { role: 'user', content: message }
    ];

    // Call Claude API
    let aiResponse: string;
    try {
      aiResponse = await callClaudeAPI(messages);
      console.log('Claude API response received successfully');
    } catch (error) {
      console.error('Claude API failed, using fallback:', error);
      aiResponse = `I'm currently experiencing technical difficulties connecting to our AI service. However, I can see you're asking about our innovation platform.

Based on our platform data, we have ${platformStats?.total_profiles || 'many'} profiles and ${platformStats?.total_posts || 'numerous'} posts from our community.

Please try your question again in a moment, or feel free to explore the platform directly. If you need immediate assistance, you can browse our community profiles and posts manually.`;
    }

    const processingTime = Date.now() - startTime;

    const response: ChatResponse = {
      success: true,
      message: aiResponse,
      rag_context_used: allContext.map(item => ({
        type: item.content_type,
        relevance: item.relevance_score || 0,
        snippet: item.content_snippet ? item.content_snippet.substring(0, 100) + '...' : 'Analytics data',
        source: item.context_source || 'unknown'
      })),
      processing_time_ms: processingTime,
      query_route: queryRouting.route,
      route_confidence: queryRouting.confidence
    };

    console.log(`AI chat completed in ${processingTime}ms`);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('AI chat error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return new Response(
      JSON.stringify({
        success: false,
        processing_time_ms: processingTime,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
