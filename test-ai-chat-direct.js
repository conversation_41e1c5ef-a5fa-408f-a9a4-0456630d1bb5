// Test AI chat functionality directly
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

async function testAIChat() {
  console.log('🧪 Testing AI Chat functionality...');
  
  const requestBody = {
    message: "Hello, can you tell me about the ZbInnovation platform?",
    conversation_history: [],
    user_context: {
      is_authenticated: true,
      user_id: "test-user-123",
      profile_type: "innovator",
      current_page: "home"
    },
    rag_enabled: true,
    max_context_items: 8
  };

  try {
    console.log('📤 Sending request to AI Edge Function...');
    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log('📥 Response status:', response.status, response.statusText);
    console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error response:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ Success! Response data:', JSON.stringify(data, null, 2));

    if (data.success && data.message) {
      console.log('🎉 AI Response received:');
      console.log('---');
      console.log(data.message);
      console.log('---');
      console.log(`⏱️ Processing time: ${data.processing_time_ms}ms`);
    } else {
      console.log('⚠️ Response received but no AI message:', data);
    }

  } catch (error) {
    console.error('❌ Error testing AI chat:', error);
  }
}

// Run the test
testAIChat();
