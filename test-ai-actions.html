<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Actions Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .action-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .action-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>AI Actions Test</h1>
    <p>This page tests the AI action functionality without requiring the full Vue.js application.</p>

    <div class="test-section">
        <h2>Navigation Actions</h2>
        <button class="action-button" onclick="testNavigation('/dashboard')">Navigate to Dashboard</button>
        <button class="action-button" onclick="testNavigation('/virtual-community')">Navigate to Community</button>
        <div id="nav-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Dialog Actions</h2>
        <button class="action-button" onclick="testDialog('auth-signin')">Trigger Sign In</button>
        <button class="action-button" onclick="testDialog('auth-signup')">Trigger Sign Up</button>
        <div id="dialog-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Trigger Actions</h2>
        <button class="action-button" onclick="testTrigger('Tell me more about this')">Send Message</button>
        <button class="action-button" onclick="testTrigger('How can I connect with these people?')">Ask About Connections</button>
        <div id="trigger-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>External Actions</h2>
        <button class="action-button" onclick="testExternal('https://example.com')">Open External URL</button>
        <div id="external-result" class="result"></div>
    </div>

    <script>
        // Mock AI Action Service for testing
        class MockAIActionService {
            async executeAction(action) {
                console.log('🎯 Executing action:', action);
                
                try {
                    switch (action.action_type) {
                        case 'navigation':
                            return this.executeNavigationAction(action);
                        case 'dialog':
                            return this.executeDialogAction(action);
                        case 'trigger':
                            return this.executeTriggerAction(action);
                        case 'external':
                            return this.executeExternalAction(action);
                        default:
                            throw new Error(`Unknown action type: ${action.action_type}`);
                    }
                } catch (error) {
                    console.error('❌ Error executing action:', error);
                    return {
                        success: false,
                        error: error.message
                    };
                }
            }

            executeNavigationAction(action) {
                const { route } = action.action_data;
                console.log('🧭 Navigating to:', route);
                
                // Simulate navigation
                return {
                    success: true,
                    message: `Would navigate to ${route}`
                };
            }

            executeDialogAction(action) {
                const { dialog } = action.action_data;
                console.log('💬 Opening dialog:', dialog);
                
                // Simulate dialog opening
                return {
                    success: true,
                    message: `Would open ${dialog} dialog`
                };
            }

            executeTriggerAction(action) {
                const { message } = action.action_data;
                console.log('🎯 Triggering message:', message);
                
                // Simulate message sending
                return {
                    success: true,
                    message: `Would send message: "${message}"`
                };
            }

            executeExternalAction(action) {
                const { url } = action.action_data;
                console.log('🌐 Opening external URL:', url);
                
                // Simulate external URL opening
                return {
                    success: true,
                    message: `Would open ${url} in new tab`
                };
            }
        }

        const actionService = new MockAIActionService();

        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.className = `result ${result.success ? 'success' : 'error'}`;
            element.textContent = result.success ? result.message : `Error: ${result.error}`;
        }

        async function testNavigation(route) {
            const action = {
                id: 'test-nav',
                label: 'Test Navigation',
                action_type: 'navigation',
                action_data: { route }
            };
            
            const result = await actionService.executeAction(action);
            showResult('nav-result', result);
        }

        async function testDialog(dialog) {
            const action = {
                id: 'test-dialog',
                label: 'Test Dialog',
                action_type: 'dialog',
                action_data: { dialog }
            };
            
            const result = await actionService.executeAction(action);
            showResult('dialog-result', result);
        }

        async function testTrigger(message) {
            const action = {
                id: 'test-trigger',
                label: 'Test Trigger',
                action_type: 'trigger',
                action_data: { message }
            };
            
            const result = await actionService.executeAction(action);
            showResult('trigger-result', result);
        }

        async function testExternal(url) {
            const action = {
                id: 'test-external',
                label: 'Test External',
                action_type: 'external',
                action_data: { url }
            };
            
            const result = await actionService.executeAction(action);
            showResult('external-result', result);
        }

        // Test on page load
        window.addEventListener('load', () => {
            console.log('✅ AI Actions Test Page Loaded');
            console.log('🎯 Ready to test AI action functionality');
        });
    </script>
</body>
</html>
