// Database Schema Checker
// This script connects to Supabase and checks the actual table structure

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabaseSchema() {
  console.log('🔍 Checking database schema...\n');

  try {
    // Check posts table structure
    console.log('📋 POSTS TABLE:');
    const { data: postsData, error: postsError } = await supabase
      .from('posts')
      .select('*')
      .limit(1);
    
    if (postsError) {
      console.log('❌ Posts table error:', postsError.message);
    } else {
      console.log('✅ Posts table exists');
      if (postsData && postsData.length > 0) {
        console.log('Sample post structure:', Object.keys(postsData[0]));
        console.log('Post ID type:', typeof postsData[0].id);
        console.log('Post user_id type:', typeof postsData[0].user_id);
        console.log('Related entity ID type:', typeof postsData[0].related_entity_id);
      }
    }

    // Check comments table structure
    console.log('\n💬 COMMENTS TABLE:');
    const { data: commentsData, error: commentsError } = await supabase
      .from('comments')
      .select('*')
      .limit(1);
    
    if (commentsError) {
      console.log('❌ Comments table error:', commentsError.message);
      
      // Try post_comments table
      console.log('Trying post_comments table...');
      const { data: postCommentsData, error: postCommentsError } = await supabase
        .from('post_comments')
        .select('*')
        .limit(1);
      
      if (postCommentsError) {
        console.log('❌ Post_comments table error:', postCommentsError.message);
      } else {
        console.log('✅ Post_comments table exists');
        if (postCommentsData && postCommentsData.length > 0) {
          console.log('Sample comment structure:', Object.keys(postCommentsData[0]));
          console.log('Comment ID type:', typeof postCommentsData[0].id);
          console.log('Comment post_id type:', typeof postCommentsData[0].post_id);
        }
      }
    } else {
      console.log('✅ Comments table exists');
      if (commentsData && commentsData.length > 0) {
        console.log('Sample comment structure:', Object.keys(commentsData[0]));
        console.log('Comment ID type:', typeof commentsData[0].id);
        console.log('Comment post_id type:', typeof commentsData[0].post_id);
      }
    }

    // Check likes table structure
    console.log('\n❤️ LIKES TABLE:');
    const { data: likesData, error: likesError } = await supabase
      .from('likes')
      .select('*')
      .limit(1);
    
    if (likesError) {
      console.log('❌ Likes table error:', likesError.message);
      
      // Try post_likes table
      console.log('Trying post_likes table...');
      const { data: postLikesData, error: postLikesError } = await supabase
        .from('post_likes')
        .select('*')
        .limit(1);
      
      if (postLikesError) {
        console.log('❌ Post_likes table error:', postLikesError.message);
      } else {
        console.log('✅ Post_likes table exists');
        if (postLikesData && postLikesData.length > 0) {
          console.log('Sample like structure:', Object.keys(postLikesData[0]));
          console.log('Like ID type:', typeof postLikesData[0].id);
          console.log('Like post_id type:', typeof postLikesData[0].post_id);
        }
      }
    } else {
      console.log('✅ Likes table exists');
      if (likesData && likesData.length > 0) {
        console.log('Sample like structure:', Object.keys(likesData[0]));
        console.log('Like ID type:', typeof likesData[0].id);
        console.log('Like post_id type:', typeof likesData[0].post_id);
      }
    }

    // Check posts_with_authors view
    console.log('\n👥 POSTS_WITH_AUTHORS VIEW:');
    const { data: viewData, error: viewError } = await supabase
      .from('posts_with_authors')
      .select('*')
      .limit(1);
    
    if (viewError) {
      console.log('❌ Posts_with_authors view error:', viewError.message);
    } else {
      console.log('✅ Posts_with_authors view exists');
      if (viewData && viewData.length > 0) {
        console.log('View structure:', Object.keys(viewData[0]));
      }
    }

    // Check for duplicate posts
    console.log('\n🔍 CHECKING FOR DUPLICATE POSTS:');
    const { data: allPosts, error: allPostsError } = await supabase
      .from('posts')
      .select('id, title, content, created_at')
      .order('created_at', { ascending: false })
      .limit(20);
    
    if (allPostsError) {
      console.log('❌ Error fetching posts:', allPostsError.message);
    } else {
      console.log(`✅ Found ${allPosts.length} recent posts`);
      
      // Check for duplicates by content
      const contentMap = new Map();
      const duplicates = [];
      
      allPosts.forEach(post => {
        const key = post.content?.substring(0, 100) || '';
        if (contentMap.has(key)) {
          duplicates.push({
            original: contentMap.get(key),
            duplicate: post
          });
        } else {
          contentMap.set(key, post);
        }
      });
      
      if (duplicates.length > 0) {
        console.log(`⚠️ Found ${duplicates.length} potential duplicate posts`);
        duplicates.forEach((dup, index) => {
          console.log(`Duplicate ${index + 1}:`);
          console.log(`  Original: ID ${dup.original.id} - ${dup.original.title || 'No title'}`);
          console.log(`  Duplicate: ID ${dup.duplicate.id} - ${dup.duplicate.title || 'No title'}`);
        });
      } else {
        console.log('✅ No duplicate posts found');
      }
    }

  } catch (error) {
    console.error('❌ Database connection error:', error);
  }
}

checkDatabaseSchema();
