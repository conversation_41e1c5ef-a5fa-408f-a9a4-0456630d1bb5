<template>
  <q-card flat bordered class="filter-card">
    <q-card-section>
      <div class="text-h6 text-primary">Filters</div>

      <!-- Dynamic AI Trigger Buttons -->
      <div class="ai-triggers-section q-mt-md q-mb-md">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="psychology" color="primary" size="sm" class="q-mr-xs" />
          AI Assistance
        </div>
        <div class="row q-gutter-xs">
          <template v-for="trigger in currentTabTriggers" :key="trigger.key">
            <AITriggerButton
              :trigger-key="trigger.key"
              :label="trigger.label"
              :icon="trigger.icon"
              :color="trigger.color || 'primary'"
              :tooltip="trigger.tooltip"
              :context="`community-${activeTab}`"
              size="sm"
              outline
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </template>
        </div>
      </div>

      <q-separator class="q-my-md" />

      <!-- Common Filters (Search & Date Range) -->
      <div class="common-filters">
        <!-- Search -->
        <div class="q-mb-md">
          <q-input
            v-model="searchQuery"
            outlined
            dense
            placeholder="Search..."
            @update:model-value="applyFilters"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </div>

        <!-- Date Range (Dropdown) -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Date Range</div>
          <q-select
            v-model="dateRange"
            :options="filterOptions.dateRangeOptions"
            outlined
            dense
            emit-value
            map-options
            option-value="value"
            option-label="label"
            @update:model-value="handleDateRangeChange"
          >
            <template v-slot:prepend>
              <q-icon name="event" color="primary" />
            </template>
            <template v-slot:append>
              <q-icon
                name="close"
                class="cursor-pointer"
                @click.stop="clearDateRange"
                v-if="filterStore.dateRange !== 'all'"
              />
            </template>
          </q-select>
        </div>
      </div>

      <!-- Dynamic Filters based on active tab -->
      <div class="dynamic-filters">
        <!-- Feed Tab Filters -->
        <template v-if="activeTab === 'feed'">
          <!-- Post Type Filter -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Post Type</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Post Types"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.postTypeOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox v-model="selectedPostTypes" :val="option.value" @update:model-value="handlePostTypeChange" />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedPostTypes.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="type in selectedPostTypes"
                :key="type"
                dense
                removable
                @remove="removePostType(type)"
                color="primary"
                text-color="white"
              >
                {{ getPostTypeLabel(type) }}
              </q-chip>
            </div>
          </div>

          <!-- Categories -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Categories</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Categories"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.categoryOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox v-model="selectedCategories" :val="option.value" @update:model-value="handleCategoryChange" />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedCategories.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="category in selectedCategories"
                :key="category"
                dense
                removable
                @remove="removeCategory(category)"
                color="primary"
                text-color="white"
              >
                {{ getCategoryLabel(category) }}
              </q-chip>
            </div>
          </div>
        </template>

        <!-- Profile Tab Filters -->
        <template v-else-if="activeTab === 'profiles'">
          <!-- Profile Types -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Profile Types</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Profile Types"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.profileTypeOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox v-model="selectedProfileTypes" :val="option.value" @update:model-value="handleProfileTypeChange" />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedProfileTypes.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="type in selectedProfileTypes"
                :key="type"
                dense
                removable
                @remove="removeProfileType(type)"
                color="primary"
                text-color="white"
              >
                {{ getProfileTypeLabel(type) }}
              </q-chip>
            </div>
          </div>


        </template>

        <!-- Blog Tab Filters -->
        <template v-else-if="activeTab === 'blog'">
          <!-- Blog Categories -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Blog Categories</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Blog Categories"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.blogCategoryOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox v-model="selectedBlogCategories" :val="option.value" @update:model-value="handleBlogCategoryChange" />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedBlogCategories.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="category in selectedBlogCategories"
                :key="category"
                dense
                removable
                @remove="removeBlogCategory(category)"
                color="primary"
                text-color="white"
              >
                {{ getBlogCategoryLabel(category) }}
              </q-chip>
            </div>
          </div>

          <!-- Read Time -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Read Time</div>
            <q-select
              v-model="selectedReadTime"
              :options="filterOptions.readTimeOptions"
              outlined
              dense
              emit-value
              map-options
              option-value="value"
              option-label="label"
              @update:model-value="handleReadTimeChange"
            />
          </div>
        </template>

        <!-- Events Tab Filters -->
        <template v-else-if="activeTab === 'events'">
          <!-- Event Types -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Event Types</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Event Types"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.eventTypeOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox v-model="selectedEventTypes" :val="option.value" @update:model-value="handleEventTypeChange" />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedEventTypes.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="type in selectedEventTypes"
                :key="type"
                dense
                removable
                @remove="removeEventType(type)"
                color="primary"
                text-color="white"
              >
                {{ getEventTypeLabel(type) }}
              </q-chip>
            </div>
          </div>

          <!-- Event Format -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Event Format</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Event Formats"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.eventFormatOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox v-model="selectedEventFormats" :val="option.value" @update:model-value="handleEventFormatChange" />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedEventFormats.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="format in selectedEventFormats"
                :key="format"
                dense
                removable
                @remove="removeEventFormat(format)"
                color="primary"
                text-color="white"
              >
                {{ getEventFormatLabel(format) }}
              </q-chip>
            </div>
          </div>
        </template>

        <!-- Groups Tab Filters -->
        <template v-else-if="activeTab === 'groups'">
          <!-- Group Categories -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Group Categories</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Group Categories"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.groupCategoryOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox v-model="selectedGroupCategories" :val="option.value" @update:model-value="handleGroupCategoryChange" />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedGroupCategories.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="category in selectedGroupCategories"
                :key="category"
                dense
                removable
                @remove="removeGroupCategory(category)"
                color="primary"
                text-color="white"
              >
                {{ getGroupCategoryLabel(category) }}
              </q-chip>
            </div>
          </div>
        </template>

        <!-- Marketplace Tab Filters -->
        <template v-else-if="activeTab === 'marketplace'">
          <!-- Listing Types -->
          <div class="q-mb-md">
            <div class="text-subtitle2 q-mb-sm">Listing Types</div>
            <q-btn
              outline
              color="primary"
              class="full-width"
              label="Select Listing Types"
              icon-right="arrow_drop_down"
            >
              <q-menu anchor="bottom left" self="top left" :offset="[0, 8]">
                <q-list style="min-width: 250px">
                  <q-item v-for="option in filterOptions.listingTypeOptions" :key="option.value">
                    <q-item-section avatar>
                      <q-checkbox
                        v-model="selectedListingTypes"
                        :val="option.value"
                        @update:model-value="handleListingTypeChange"
                      />
                    </q-item-section>
                    <q-item-section avatar>
                      <q-icon :name="option.icon" color="primary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ option.label }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <div v-if="selectedListingTypes.length > 0" class="selected-filters q-mt-sm">
              <q-chip
                v-for="type in selectedListingTypes"
                :key="type"
                dense
                removable
                @remove="removeListingType(type)"
                color="primary"
                text-color="white"
              >
                {{ getListingTypeLabel(type) }}
              </q-chip>
            </div>
          </div>
        </template>
      </div>

      <!-- Reset Filters -->
      <div class="q-mt-lg">
        <q-btn
          outline
          color="grey"
          label="Reset Filters"
          class="full-width q-mb-md"
          @click="resetFilters"
        />
        <q-btn
          color="primary"
          label="Apply Filters"
          class="full-width"
          @click="applyFilters"
        />
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, watch, computed, ref } from 'vue';
import { useFilterStore } from '../../stores/filterStore';
import { filterOptions } from '../../services/filterOptionsService';
import AITriggerButton from '../ai/AITriggerButton.vue';

const props = defineProps({
  activeTab: {
    type: String,
    default: 'feed'
  }
});

const emit = defineEmits(['filter-changed']);

// Use the filter store
const filterStore = useFilterStore();

// AI Trigger definitions for each tab
const aiTriggers = {
  feed: [
    {
      key: 'content_discovery',
      label: 'Discover Content',
      icon: 'explore',
      color: 'primary',
      tooltip: 'Get AI help finding relevant content and discussions'
    },
    {
      key: 'networking',
      label: 'Find Connections',
      icon: 'people',
      color: 'secondary',
      tooltip: 'Get AI help with networking and connecting with people'
    }
  ],
  profiles: [
    {
      key: 'networking',
      label: 'Find Connections',
      icon: 'people',
      color: 'primary',
      tooltip: 'Get AI help finding people to connect with'
    },
    {
      key: 'collaboration',
      label: 'Collaboration',
      icon: 'handshake',
      color: 'secondary',
      tooltip: 'Get AI help finding collaboration opportunities'
    }
  ],
  blog: [
    {
      key: 'content_discovery',
      label: 'Discover Articles',
      icon: 'article',
      color: 'primary',
      tooltip: 'Get AI help finding relevant blog posts and articles'
    },
    {
      key: 'content_ideas',
      label: 'Content Ideas',
      icon: 'lightbulb',
      color: 'secondary',
      tooltip: 'Get AI help with content creation ideas'
    }
  ],
  events: [
    {
      key: 'event_discovery',
      label: 'Find Events',
      icon: 'event',
      color: 'primary',
      tooltip: 'Get AI help finding relevant events and workshops'
    },
    {
      key: 'networking',
      label: 'Event Networking',
      icon: 'groups',
      color: 'secondary',
      tooltip: 'Get AI help with event networking strategies'
    }
  ],
  groups: [
    {
      key: 'group_discovery',
      label: 'Find Groups',
      icon: 'groups',
      color: 'primary',
      tooltip: 'Get AI help finding relevant groups to join'
    },
    {
      key: 'collaboration',
      label: 'Group Collaboration',
      icon: 'handshake',
      color: 'secondary',
      tooltip: 'Get AI help with group collaboration opportunities'
    }
  ],
  marketplace: [
    {
      key: 'product_discovery',
      label: 'Find Products',
      icon: 'storefront',
      color: 'primary',
      tooltip: 'Get AI help finding relevant products and services'
    },
    {
      key: 'business_opportunities',
      label: 'Business Opportunities',
      icon: 'business',
      color: 'secondary',
      tooltip: 'Get AI help identifying business opportunities'
    }
  ]
};

// Computed property for current tab triggers
const currentTabTriggers = computed(() => {
  return aiTriggers[props.activeTab as keyof typeof aiTriggers] || [];
});

// AI Trigger Handlers
const onTriggerActivated = (triggerKey: string) => {
  console.log('AI trigger activated:', triggerKey);
};

const onTriggerSuccess = (triggerKey: string) => {
  console.log('AI trigger successful:', triggerKey);
};

const onTriggerError = (triggerKey: string, error: Error) => {
  console.error('AI trigger error:', triggerKey, error);
};

// Local state for selected values - Feed tab
const selectedPostTypes = ref([]);
const selectedCategories = ref([]);

// Local state for selected values - Profile tab
const selectedProfileTypes = ref([]);

// Local state for selected values - Blog tab
const selectedBlogCategories = ref([]);
const selectedReadTime = ref('any');

// Local state for selected values - Events tab
const selectedEventTypes = ref([]);
const selectedEventFormats = ref([]);

// Local state for selected values - Groups tab
const selectedGroupCategories = ref([]);

// Local state for selected values - Marketplace tab
const selectedListingTypes = ref([]);

// Computed properties to access store state
const searchQuery = computed({
  get: () => filterStore.searchQuery,
  set: (value) => filterStore.setSearchQuery(value)
});

const dateRange = computed({
  get: () => {
    const currentValue = filterStore.dateRange;
    return filterOptions.dateRangeOptions.find(option => option.value === currentValue) || filterOptions.dateRangeOptions[0];
  },
  set: (value) => {
    const dateRangeValue = typeof value === 'string' ? value : value.value;
    filterStore.setDateRange(dateRangeValue);
  }
});

// Watch for changes in the active tab
watch(() => props.activeTab, (newTab) => {
  filterStore.setActiveTab(newTab);
  updateLocalState();
}, { immediate: true });

// Watch for changes in the store state - Feed tab
watch(() => filterStore.feedFilters.postTypes, (newValue) => {
  selectedPostTypes.value = [...newValue];
}, { immediate: true });

watch(() => filterStore.feedFilters.categories, (newValue) => {
  selectedCategories.value = [...newValue];
}, { immediate: true });

// Watch for changes in the store state - Profile tab
watch(() => filterStore.profileFilters.profileTypes, (newValue) => {
  selectedProfileTypes.value = [...newValue];
}, { immediate: true });



// Watch for changes in the store state - Blog tab
watch(() => filterStore.blogFilters.blogCategories, (newValue) => {
  selectedBlogCategories.value = [...newValue];
}, { immediate: true });

watch(() => filterStore.blogFilters.readTime, (newValue) => {
  selectedReadTime.value = newValue;
}, { immediate: true });

// Watch for changes in the store state - Events tab
watch(() => filterStore.eventFilters.eventTypes, (newValue) => {
  selectedEventTypes.value = [...newValue];
}, { immediate: true });

watch(() => filterStore.eventFilters.eventFormat, (newValue) => {
  selectedEventFormats.value = [...newValue];
}, { immediate: true });

// Watch for changes in the store state - Groups tab
watch(() => filterStore.groupFilters.groupCategories, (newValue) => {
  selectedGroupCategories.value = [...newValue];
}, { immediate: true });

// Watch for changes in the store state - Marketplace tab
watch(() => filterStore.marketplaceFilters.listingTypes, (newValue) => {
  selectedListingTypes.value = [...newValue];
}, { immediate: true });

// Helper methods - Feed tab
function getPostTypeLabel(value) {
  const option = filterOptions.postTypeOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}

function getCategoryLabel(value) {
  const option = filterOptions.categoryOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}

// Helper methods - Profile tab
function getProfileTypeLabel(value) {
  const option = filterOptions.profileTypeOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}



// Helper methods - Blog tab
function getBlogCategoryLabel(value) {
  const option = filterOptions.blogCategoryOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}

// Helper methods - Events tab
function getEventTypeLabel(value) {
  const option = filterOptions.eventTypeOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}

function getEventFormatLabel(value) {
  const option = filterOptions.eventFormatOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}

// Helper methods - Groups tab
function getGroupCategoryLabel(value) {
  const option = filterOptions.groupCategoryOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}

// Helper methods - Marketplace tab
function getListingTypeLabel(value) {
  const option = filterOptions.listingTypeOptions.find(opt => opt.value === value);
  return option ? option.label : value;
}

// Event handlers - Common
function handleDateRangeChange(value) {
  const dateRangeValue = typeof value === 'string' ? value : value.value;
  filterStore.setDateRange(dateRangeValue);
  applyFilters();
}

// Event handlers - Feed tab
function handlePostTypeChange() {
  filterStore.updateFeedFilters({ postTypes: [...selectedPostTypes.value] });
  applyFilters();
}

function handleCategoryChange() {
  filterStore.updateFeedFilters({ categories: [...selectedCategories.value] });
  applyFilters();
}

function removePostType(value) {
  selectedPostTypes.value = selectedPostTypes.value.filter(type => type !== value);
  handlePostTypeChange();
}

function removeCategory(value) {
  selectedCategories.value = selectedCategories.value.filter(cat => cat !== value);
  handleCategoryChange();
}

// Event handlers - Profile tab
function handleProfileTypeChange() {
  filterStore.updateProfileFilters({ profileTypes: [...selectedProfileTypes.value] });
  applyFilters();
}

function removeProfileType(value) {
  selectedProfileTypes.value = selectedProfileTypes.value.filter(type => type !== value);
  handleProfileTypeChange();
}

// Event handlers - Blog tab
function handleBlogCategoryChange() {
  filterStore.updateBlogFilters({ blogCategories: [...selectedBlogCategories.value] });
  applyFilters();
}

function handleReadTimeChange(value) {
  filterStore.updateBlogFilters({ readTime: value });
  applyFilters();
}

function removeBlogCategory(value) {
  selectedBlogCategories.value = selectedBlogCategories.value.filter(cat => cat !== value);
  handleBlogCategoryChange();
}

// Event handlers - Events tab
function handleEventTypeChange() {
  filterStore.updateEventFilters({ eventTypes: [...selectedEventTypes.value] });
  applyFilters();
}

function handleEventFormatChange() {
  filterStore.updateEventFilters({ eventFormat: [...selectedEventFormats.value] });
  applyFilters();
}

function removeEventType(value) {
  selectedEventTypes.value = selectedEventTypes.value.filter(type => type !== value);
  handleEventTypeChange();
}

function removeEventFormat(value) {
  selectedEventFormats.value = selectedEventFormats.value.filter(format => format !== value);
  handleEventFormatChange();
}

// Event handlers - Groups tab
function handleGroupCategoryChange() {
  filterStore.updateGroupFilters({ groupCategories: [...selectedGroupCategories.value] });
  applyFilters();
}

function removeGroupCategory(value) {
  selectedGroupCategories.value = selectedGroupCategories.value.filter(cat => cat !== value);
  handleGroupCategoryChange();
}

// Event handlers - Marketplace tab
function handleListingTypeChange() {
  filterStore.updateMarketplaceFilters({ listingTypes: [...selectedListingTypes.value] });
  applyFilters();
}

function removeListingType(value) {
  selectedListingTypes.value = selectedListingTypes.value.filter(type => type !== value);
  handleListingTypeChange();
}

function clearDateRange() {
  filterStore.setDateRange('all');
  applyFilters();
}

function resetFilters() {
  filterStore.resetCurrentTabFilters();
  updateLocalState();
  applyFilters();
}

function updateLocalState() {
  // Update local state based on the current active tab
  switch (props.activeTab) {
    case 'feed':
      selectedPostTypes.value = [...filterStore.feedFilters.postTypes];
      selectedCategories.value = [...filterStore.feedFilters.categories];
      break;
    case 'profiles':
      selectedProfileTypes.value = [...filterStore.profileFilters.profileTypes];
      break;
    case 'blog':
      selectedBlogCategories.value = [...filterStore.blogFilters.blogCategories];
      selectedReadTime.value = filterStore.blogFilters.readTime;
      break;
    case 'events':
      selectedEventTypes.value = [...filterStore.eventFilters.eventTypes];
      selectedEventFormats.value = [...filterStore.eventFilters.eventFormat];
      break;
    case 'groups':
      selectedGroupCategories.value = [...filterStore.groupFilters.groupCategories];
      break;
    case 'marketplace':
      selectedListingTypes.value = [...filterStore.marketplaceFilters.listingTypes];
      break;
  }
}

function applyFilters() {
  emit('filter-changed', filterStore.currentFilters);
}
</script>

<style scoped>
.filter-card {
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.ai-triggers-section {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 12px;
}

.ai-trigger-btn {
  min-height: 32px;
  font-size: 0.8rem;
}

.dynamic-filters {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

.selected-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

@media (max-width: 767px) {
  .filter-card {
    position: static;
    max-height: none;
    overflow-y: visible;
    margin: 0 8px;
    border-radius: 8px;
    width: calc(100% - 16px);
  }

  .q-card__section {
    padding: 16px;
  }

  /* Fix row gutter issues */
  .row.q-gutter-xs {
    margin-left: 0;
  }

  .selected-filters {
    margin-left: 0;
  }
}
</style>
