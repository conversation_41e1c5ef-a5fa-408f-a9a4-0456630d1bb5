// Debug Claude API issue
const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

async function callEdgeFunction(functionName, body = {}, method = 'POST') {
  const headers = {
    'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
    'apikey': SUPABASE_ANON_KEY,
    'Content-Type': 'application/json',
  };

  const config = { method, headers };
  if (method !== 'GET' && body) {
    config.body = JSON.stringify(body);
  }

  const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, config);
  
  return {
    ok: response.ok,
    status: response.status,
    statusText: response.statusText,
    data: response.ok ? await response.json() : await response.text()
  };
}

async function debugClaudeAPI() {
  console.log('🔍 Debugging Claude API Issue...\n');
  
  // Test 1: Simple test message
  console.log('Test 1: Simple message to Claude API');
  try {
    const result = await callEdgeFunction('ai-chat', {
      message: "Just say 'Hello from Claude' - this is a test",
      user_context: {},
      rag_enabled: false,
      max_context_items: 0
    });

    console.log(`Status: ${result.status}`);
    if (result.ok) {
      const data = result.data;
      console.log(`Success: ${data.success}`);
      console.log(`Message: "${data.message}"`);
      console.log(`Processing time: ${data.processing_time_ms}ms`);
      
      // Check if it's a fallback message
      if (data.message && data.message.includes('system upgrade')) {
        console.log('❌ Still getting fallback message - Claude API not working');
        return false;
      } else if (data.message && data.message.toLowerCase().includes('hello')) {
        console.log('✅ Real Claude response received!');
        return true;
      }
    } else {
      console.log(`❌ Error: ${result.data}`);
    }
  } catch (error) {
    console.log(`❌ Exception: ${error.message}`);
  }
  
  return false;
}

async function checkSupabaseSecrets() {
  console.log('\n🔑 Checking Supabase Environment Variables...');
  
  // We can't directly access environment variables from the client
  // But we can test if the Edge Function has access to them
  try {
    const result = await callEdgeFunction('ai-chat', {
      message: "Debug: Check if CLAUDE_API_KEY is available",
      user_context: { debug: true },
      rag_enabled: false
    });

    if (result.ok) {
      console.log('✅ Edge Function is accessible');
      console.log('Response:', result.data);
    } else {
      console.log('❌ Edge Function error:', result.status, result.data);
    }
  } catch (error) {
    console.log('❌ Error calling Edge Function:', error.message);
  }
}

async function testRAGPopulation() {
  console.log('\n📚 Testing RAG Population with smaller batch...');
  
  try {
    // Try with very small batch size
    const result = await callEdgeFunction('populate-rag-embeddings', {
      content_types: ['profile'],
      batch_size: 2, // Very small batch
      force_regenerate: false,
      dry_run: false
    });

    if (result.ok) {
      const data = result.data;
      console.log(`✅ RAG Population Status:`);
      console.log(`   Success: ${data.success}`);
      console.log(`   Total embeddings: ${data.total_embeddings_created || 0}`);
      console.log(`   Processing time: ${data.processing_time_ms}ms`);
      
      if (data.content_processed) {
        data.content_processed.forEach(content => {
          console.log(`   ${content.content_type}: ${content.embeddings_created}/${content.records_found}`);
        });
      }
      
      return data.total_embeddings_created > 0;
    } else {
      console.log(`❌ RAG Population failed: ${result.status}`);
      console.log(`   Error: ${result.data}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ RAG Population error: ${error.message}`);
    return false;
  }
}

async function runDebugTests() {
  console.log('🚀 Starting Debug Tests...\n');
  console.log('=' .repeat(60));
  
  // Test Claude API
  const claudeWorking = await debugClaudeAPI();
  
  console.log('\n' + '=' .repeat(60));
  
  // Check Supabase secrets
  await checkSupabaseSecrets();
  
  console.log('\n' + '=' .repeat(60));
  
  // Test RAG population with smaller batch
  const ragWorking = await testRAGPopulation();
  
  console.log('\n' + '=' .repeat(60));
  console.log('\n📊 Debug Results Summary:');
  console.log(`   Claude API: ${claudeWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);
  console.log(`   RAG Population: ${ragWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);
  
  if (!claudeWorking) {
    console.log('\n🔧 Claude API Troubleshooting:');
    console.log('   1. Check if CLAUDE_API_KEY is set in Supabase dashboard');
    console.log('   2. Verify API key is valid and has credits');
    console.log('   3. Check Edge Function logs for detailed errors');
    console.log('   4. Ensure correct API key format (starts with sk-)');
  }
  
  if (!ragWorking) {
    console.log('\n🔧 RAG Population Troubleshooting:');
    console.log('   1. Check if HUGGINGFACE_EMBEDDING_API_KEY is set');
    console.log('   2. Verify embedding service is accessible');
    console.log('   3. Check database permissions for embeddings table');
    console.log('   4. Try even smaller batch sizes (batch_size: 1)');
  }
  
  console.log('\n🎯 Next Steps:');
  if (claudeWorking && ragWorking) {
    console.log('   ✅ Both systems working - check frontend integration');
  } else {
    console.log('   🔧 Fix backend issues before testing frontend');
  }
}

runDebugTests();
